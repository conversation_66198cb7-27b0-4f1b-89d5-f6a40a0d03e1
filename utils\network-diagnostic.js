/**
 * 设备监控系统 - 网络诊断工具
 * 用于诊断网络连接和API接口问题
 * 创建时间: 2025-01-07
 */

import { getCurrentConfig } from '../config/env.js'

/**
 * 网络诊断工具类
 */
class NetworkDiagnostic {
  constructor() {
    this.config = getCurrentConfig()
  }

  /**
   * 完整的网络诊断
   */
  async runFullDiagnostic() {
    console.log('🔍 开始网络诊断...')
    
    const results = {
      networkStatus: await this.checkNetworkStatus(),
      serverReachability: await this.checkServerReachability(),
      apiHealth: await this.checkApiHealth(),
      wxLoginCode: await this.checkWxLoginCode()
    }
    
    console.log('📊 诊断结果:', results)
    return results
  }

  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          const result = {
            success: true,
            networkType: res.networkType,
            isConnected: res.networkType !== 'none'
          }
          console.log('📶 网络状态:', result)
          resolve(result)
        },
        fail: (error) => {
          const result = {
            success: false,
            error: error.errMsg
          }
          console.error('❌ 获取网络状态失败:', result)
          resolve(result)
        }
      })
    })
  }

  /**
   * 检查服务器可达性
   */
  async checkServerReachability() {
    return new Promise((resolve) => {
      const startTime = Date.now()
      
      wx.request({
        url: this.config.API_BASE_URL,
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          const result = {
            success: true,
            statusCode: res.statusCode,
            responseTime: Date.now() - startTime,
            server: this.config.API_BASE_URL
          }
          console.log('🌐 服务器可达性:', result)
          resolve(result)
        },
        fail: (error) => {
          const result = {
            success: false,
            error: error.errMsg,
            responseTime: Date.now() - startTime,
            server: this.config.API_BASE_URL
          }
          console.error('❌ 服务器不可达:', result)
          resolve(result)
        }
      })
    })
  }

  /**
   * 检查API健康状态
   */
  async checkApiHealth() {
    return new Promise((resolve) => {
      const healthUrl = this.config.API_BASE_URL + '/health'
      const startTime = Date.now()
      
      wx.request({
        url: healthUrl,
        method: 'GET',
        timeout: 5000,
        success: (res) => {
          const result = {
            success: true,
            statusCode: res.statusCode,
            data: res.data,
            responseTime: Date.now() - startTime,
            url: healthUrl
          }
          console.log('💚 API健康检查:', result)
          resolve(result)
        },
        fail: (error) => {
          const result = {
            success: false,
            error: error.errMsg,
            responseTime: Date.now() - startTime,
            url: healthUrl
          }
          console.error('❌ API健康检查失败:', result)
          resolve(result)
        }
      })
    })
  }

  /**
   * 检查微信登录Code获取
   */
  async checkWxLoginCode() {
    return new Promise((resolve) => {
      wx.login({
        success: (res) => {
          const result = {
            success: true,
            code: res.code,
            codeLength: res.code ? res.code.length : 0
          }
          console.log('🔑 微信登录Code:', result)
          resolve(result)
        },
        fail: (error) => {
          const result = {
            success: false,
            error: error.errMsg
          }
          console.error('❌ 获取微信登录Code失败:', result)
          resolve(result)
        }
      })
    })
  }

  /**
   * 测试微信登录接口
   */
  async testWxLoginApi(code) {
    return new Promise((resolve) => {
      const loginUrl = this.config.API_BASE_URL + '/wx/login'
      const startTime = Date.now()
      
      wx.request({
        url: loginUrl,
        method: 'POST',
        data: {
          code: code,
          userInfo: {
            nickName: 'Test User',
            avatarUrl: ''
          }
        },
        header: {
          'Content-Type': 'application/json'
        },
        timeout: 10000,
        success: (res) => {
          const result = {
            success: true,
            statusCode: res.statusCode,
            data: res.data,
            responseTime: Date.now() - startTime,
            url: loginUrl
          }
          console.log('🔐 微信登录接口测试:', result)
          resolve(result)
        },
        fail: (error) => {
          const result = {
            success: false,
            error: error.errMsg,
            responseTime: Date.now() - startTime,
            url: loginUrl
          }
          console.error('❌ 微信登录接口测试失败:', result)
          resolve(result)
        }
      })
    })
  }

  /**
   * 显示诊断报告
   */
  async showDiagnosticReport() {
    wx.showLoading({ title: '诊断中...' })
    
    try {
      const results = await this.runFullDiagnostic()
      wx.hideLoading()
      
      // 生成报告文本
      let report = '📊 网络诊断报告\n\n'
      
      // 网络状态
      if (results.networkStatus.success) {
        report += `📶 网络状态: ${results.networkStatus.networkType}\n`
      } else {
        report += `❌ 网络状态: 检查失败\n`
      }
      
      // 服务器可达性
      if (results.serverReachability.success) {
        report += `🌐 服务器: 可达 (${results.serverReachability.responseTime}ms)\n`
      } else {
        report += `❌ 服务器: 不可达\n`
      }
      
      // API健康状态
      if (results.apiHealth.success) {
        report += `💚 API: 正常 (${results.apiHealth.statusCode})\n`
      } else {
        report += `❌ API: 异常\n`
      }
      
      // 微信登录Code
      if (results.wxLoginCode.success) {
        report += `🔑 微信Code: 正常\n`
      } else {
        report += `❌ 微信Code: 获取失败\n`
      }
      
      report += `\n🔧 当前环境: ${this.config.ENV_NAME}\n`
      report += `📡 API地址: ${this.config.API_BASE_URL}`
      
      wx.showModal({
        title: '网络诊断',
        content: report,
        showCancel: false,
        confirmText: '确定'
      })
      
    } catch (error) {
      wx.hideLoading()
      wx.showModal({
        title: '诊断失败',
        content: `诊断过程中出现错误: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      })
    }
  }

  /**
   * 快速连接测试
   */
  async quickConnectionTest() {
    wx.showLoading({ title: '测试连接...' })
    
    try {
      const networkStatus = await this.checkNetworkStatus()
      
      if (!networkStatus.isConnected) {
        wx.hideLoading()
        wx.showToast({
          title: '网络未连接',
          icon: 'error'
        })
        return false
      }
      
      const serverTest = await this.checkServerReachability()
      wx.hideLoading()
      
      if (serverTest.success) {
        wx.showToast({
          title: '连接正常',
          icon: 'success'
        })
        return true
      } else {
        wx.showToast({
          title: '服务器连接失败',
          icon: 'error'
        })
        return false
      }
      
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '测试失败',
        icon: 'error'
      })
      return false
    }
  }
}

// 创建全局实例
const networkDiagnostic = new NetworkDiagnostic()

// 导出工具函数
export {
  NetworkDiagnostic,
  networkDiagnostic
}

// 默认导出
export default networkDiagnostic
