// pages/device/detail.js
import { getDeviceDetail } from '../../api/device.js'
import { checkPagePermission } from '../../utils/permission.js'
import { showError, showSuccess } from '../../utils/request.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 设备ID
    deviceId: null,
    // 设备详细信息
    deviceInfo: null,
    // 加载状态
    loading: true,
    // 错误状态
    hasError: false,
    // 设备图片列表
    pictureUrls: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备详情页面加载, 参数:', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取设备ID
    const deviceId = options.id
    if (!deviceId) {
      showError('设备ID不能为空')
      wx.navigateBack()
      return
    }

    this.setData({ deviceId: parseInt(deviceId) })
    
    // 加载设备详情
    this.loadDeviceDetail()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('设备详情页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新设备详情')
    this.loadDeviceDetail().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `设备详情 - ${this.data.deviceInfo?.deviceName || '设备监控系统'}`,
      path: `/pages/device/detail?id=${this.data.deviceId}`
    }
  },

  /**
   * 加载设备详情
   */
  async loadDeviceDetail() {
    if (!this.data.deviceId) {
      return
    }

    this.setData({ 
      loading: true,
      hasError: false
    })

    try {
      console.log('📤 加载设备详情, ID:', this.data.deviceId)
      const result = await getDeviceDetail(this.data.deviceId)
      
      console.log('📥 设备详情响应:', result)

      if (result && result.data) {
        const deviceInfo = result.data
        
        // 处理图片URL列表
        const pictureUrls = deviceInfo.pictureUrls || []
        
        this.setData({
          deviceInfo: deviceInfo,
          pictureUrls: pictureUrls,
          loading: false,
          hasError: false
        })

        // 更新页面标题
        if (deviceInfo.deviceName) {
          wx.setNavigationBarTitle({
            title: deviceInfo.deviceName
          })
        }
      } else {
        throw new Error('设备详情数据格式错误')
      }
    } catch (error) {
      console.error('❌ 加载设备详情失败:', error)
      this.setData({ 
        loading: false,
        hasError: true
      })
      showError('加载设备详情失败')
    }
  },

  /**
   * 重试加载
   */
  onRetryLoad() {
    this.loadDeviceDetail()
  },

  /**
   * 预览设备图片
   */
  onPreviewImage(event) {
    const current = event.currentTarget.dataset.url
    const urls = this.data.pictureUrls
    
    if (urls.length > 0) {
      wx.previewImage({
        current: current,
        urls: urls
      })
    }
  },

  /**
   * 获取设备状态文本
   */
  getDeviceStatusText(status) {
    switch (status) {
      case 1:
        return '在线'
      case 0:
        return '离线'
      default:
        return '未知'
    }
  },

  /**
   * 获取设备状态样式
   */
  getDeviceStatusClass(status) {
    switch (status) {
      case 1:
        return 'success'
      case 0:
        return 'default'
      default:
        return 'warning'
    }
  },

  /**
   * 格式化日期
   */
  formatDate(dateStr) {
    if (!dateStr) return '未设置'

    try {
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN')
    } catch (error) {
      return dateStr
    }
  },

  /**
   * 查看实时数据
   */
  onViewRealTimeData() {
    const deviceId = this.data.deviceId
    const deviceName = this.data.deviceInfo?.deviceName || '设备'

    console.log('🔥 查看实时数据, 设备ID:', deviceId, '设备名称:', deviceName)

    // 跳转到实时数据页面
    wx.navigateTo({
      url: `/pages/realtime/data?id=${deviceId}&name=${encodeURIComponent(deviceName)}`,
      success: () => {
        console.log('✅ 跳转实时数据页面成功')
      },
      fail: (error) => {
        console.error('❌ 跳转实时数据页面失败:', error)
        showError('跳转失败，请重试')
      }
    })
  }
})
