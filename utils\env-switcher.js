/**
 * 设备监控系统 - 环境切换工具
 * 开发时快速切换环境的工具函数
 * 创建时间: 2025-01-07
 */

import { getCurrentConfig, showEnvInfo, ENV_TYPE } from '../config/env.js'

/**
 * 环境切换器组件
 * 仅在开发环境显示
 */
class EnvSwitcher {
  constructor() {
    this.currentConfig = getCurrentConfig()
    this.isVisible = this.currentConfig.DEBUG
  }

  /**
   * 显示环境切换面板
   */
  show() {
    if (!this.isVisible) return

    const envOptions = [
      '开发环境 (localhost)',
      '测试环境 (test)',
      '预发布环境 (staging)',
      '生产环境 (production)'
    ]

    wx.showActionSheet({
      itemList: envOptions,
      success: (res) => {
        const envTypes = [
          ENV_TYPE.DEVELOPMENT,
          ENV_TYPE.TESTING,
          ENV_TYPE.STAGING,
          ENV_TYPE.PRODUCTION
        ]
        
        const selectedEnv = envTypes[res.tapIndex]
        this.switchEnvironment(selectedEnv)
      }
    })
  }

  /**
   * 切换环境
   */
  switchEnvironment(envType) {
    // 注意：这里只是演示，实际切换需要重新加载配置
    wx.showModal({
      title: '环境切换',
      content: `要切换到 ${envType} 环境吗？\n\n注意：这需要重启小程序才能生效。`,
      confirmText: '确认切换',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 保存环境选择到本地存储
          wx.setStorageSync('MANUAL_ENV_TYPE', envType)
          
          // 提示重启
          wx.showToast({
            title: '请重启小程序',
            icon: 'success',
            duration: 2000
          })
        }
      }
    })
  }

  /**
   * 显示当前环境信息
   */
  showCurrentEnv() {
    showEnvInfo()
  }

  /**
   * 创建环境切换按钮（开发时使用）
   */
  createSwitchButton() {
    if (!this.isVisible) return null

    return {
      text: `环境: ${this.currentConfig.ENV_NAME}`,
      handler: () => this.show()
    }
  }
}

/**
 * 全局环境切换器实例
 */
const envSwitcher = new EnvSwitcher()

/**
 * 在页面中添加环境切换功能
 * 使用方法：在页面的 onLoad 中调用
 */
function addEnvSwitcher(page) {
  if (!envSwitcher.isVisible) return

  // 添加长按标题栏切换环境的功能
  const originalOnLoad = page.onLoad || function() {}
  
  page.onLoad = function(options) {
    // 调用原始 onLoad
    originalOnLoad.call(this, options)
    
    // 添加环境切换功能
    this.switchEnv = () => envSwitcher.show()
    this.showEnvInfo = () => envSwitcher.showCurrentEnv()
  }
}

/**
 * 环境调试面板
 */
function showDebugPanel() {
  const config = getCurrentConfig()
  
  if (!config.DEBUG) return

  const debugInfo = [
    `环境: ${config.ENV_NAME}`,
    `API: ${config.API_BASE_URL}`,
    `WebSocket: ${config.WS_BASE_URL}`,
    `调试模式: ${config.DEBUG ? '开启' : '关闭'}`,
    `日志级别: ${config.LOG_LEVEL}`
  ]

  wx.showModal({
    title: '调试信息',
    content: debugInfo.join('\n'),
    showCancel: false,
    confirmText: '关闭'
  })
}

/**
 * 网络状态检查
 */
function checkNetworkStatus() {
  wx.getNetworkType({
    success: (res) => {
      const networkType = res.networkType
      
      if (networkType === 'none') {
        wx.showToast({
          title: '网络连接失败',
          icon: 'error'
        })
      } else {
        console.log('网络类型:', networkType)
      }
    }
  })
}

/**
 * API连通性测试
 */
async function testApiConnection() {
  const config = getCurrentConfig()
  
  try {
    wx.showLoading({ title: '测试连接...' })
    
    const result = await new Promise((resolve, reject) => {
      wx.request({
        url: config.API_BASE_URL + '/health',
        method: 'GET',
        timeout: 5000,
        success: resolve,
        fail: reject
      })
    })
    
    wx.hideLoading()
    
    if (result.statusCode === 200) {
      wx.showToast({
        title: 'API连接正常',
        icon: 'success'
      })
    } else {
      wx.showToast({
        title: `连接异常: ${result.statusCode}`,
        icon: 'error'
      })
    }
    
  } catch (error) {
    wx.hideLoading()
    wx.showToast({
      title: 'API连接失败',
      icon: 'error'
    })
    console.error('API连接测试失败:', error)
  }
}

// 导出工具函数
export {
  EnvSwitcher,
  envSwitcher,
  addEnvSwitcher,
  showDebugPanel,
  checkNetworkStatus,
  testApiConnection
}

// 默认导出环境切换器
export default envSwitcher
