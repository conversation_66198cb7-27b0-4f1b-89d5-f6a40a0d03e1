/**
 * 设备监控系统 - 环境配置
 * 根据不同环境自动切换后端服务地址
 * 创建时间: 2025-01-07
 */

// 环境类型
const ENV_TYPE = {
  DEVELOPMENT: 'development',  // 开发环境
  TESTING: 'testing',         // 测试环境
  STAGING: 'staging',         // 预发布环境
  PRODUCTION: 'production'    // 生产环境
}

// 环境配置
const ENV_CONFIG = {
  // 开发环境
  [ENV_TYPE.DEVELOPMENT]: {
    API_BASE_URL: 'http://localhost:8080',
    WS_BASE_URL: 'ws://localhost:8080/ws',
    ENV_NAME: '开发环境',
    DEBUG: true,
    LOG_LEVEL: 'debug'
  },

  // 测试环境（请修改为您的测试服务器地址）
  [ENV_TYPE.TESTING]: {
    API_BASE_URL: 'http://your-test-server.com:8080',
    WS_BASE_URL: 'ws://your-test-server.com:8080/ws',
    ENV_NAME: '测试环境',
    DEBUG: true,
    LOG_LEVEL: 'info'
  },

  // 预发布环境（请修改为您的预发布服务器地址）
  [ENV_TYPE.STAGING]: {
    API_BASE_URL: 'http://your-staging-server.com',
    WS_BASE_URL: 'ws://your-staging-server.com/ws',
    ENV_NAME: '预发布环境',
    DEBUG: false,
    LOG_LEVEL: 'warn'
  },

  // 生产环境（请修改为您的生产服务器地址）
  [ENV_TYPE.PRODUCTION]: {
    API_BASE_URL: 'https://your-production-server.com',
    WS_BASE_URL: 'wss://your-production-server.com/ws',
    ENV_NAME: '生产环境',
    DEBUG: false,
    LOG_LEVEL: 'error'
  }
}

/**
 * 获取当前环境类型
 * 可以通过多种方式判断环境
 */
function getCurrentEnv() {
  // 方法1: 通过编译条件判断（推荐）
  // 在微信开发者工具中可以通过 __wxConfig 获取编译信息
  if (typeof __wxConfig !== 'undefined') {
    const envVersion = __wxConfig.envVersion
    switch (envVersion) {
      case 'develop':
        return ENV_TYPE.DEVELOPMENT
      case 'trial':
        return ENV_TYPE.TESTING
      case 'release':
        return ENV_TYPE.PRODUCTION
      default:
        return ENV_TYPE.DEVELOPMENT
    }
  }
  
  // 方法2: 通过 wx.getAccountInfoSync() 判断
  try {
    const accountInfo = wx.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion
    switch (envVersion) {
      case 'develop':
        return ENV_TYPE.DEVELOPMENT
      case 'trial':
        return ENV_TYPE.TESTING
      case 'release':
        return ENV_TYPE.PRODUCTION
      default:
        return ENV_TYPE.DEVELOPMENT
    }
  } catch (e) {
    console.warn('获取账号信息失败，使用默认开发环境')
  }
  
  // 方法3: 手动设置（开发时临时使用）
  // 注意：发布前要注释掉或删除这行
  // return ENV_TYPE.PRODUCTION
  
  // 默认返回开发环境
  return ENV_TYPE.DEVELOPMENT
}

/**
 * 获取当前环境配置
 */
function getCurrentConfig() {
  const currentEnv = getCurrentEnv()
  const config = ENV_CONFIG[currentEnv]
  
  console.log(`当前环境: ${config.ENV_NAME} (${currentEnv})`)
  console.log(`API地址: ${config.API_BASE_URL}`)
  
  return {
    ...config,
    ENV_TYPE: currentEnv
  }
}

/**
 * 是否为开发环境
 */
function isDevelopment() {
  return getCurrentEnv() === ENV_TYPE.DEVELOPMENT
}

/**
 * 是否为生产环境
 */
function isProduction() {
  return getCurrentEnv() === ENV_TYPE.PRODUCTION
}

/**
 * 是否启用调试模式
 */
function isDebugMode() {
  return getCurrentConfig().DEBUG
}

/**
 * 获取API基础地址
 */
function getApiBaseUrl() {
  return getCurrentConfig().API_BASE_URL
}

/**
 * 获取WebSocket基础地址
 */
function getWsBaseUrl() {
  return getCurrentConfig().WS_BASE_URL
}

/**
 * 获取日志级别
 */
function getLogLevel() {
  return getCurrentConfig().LOG_LEVEL
}

/**
 * 环境信息显示（调试用）
 */
function showEnvInfo() {
  const config = getCurrentConfig()
  
  if (config.DEBUG) {
    wx.showModal({
      title: '环境信息',
      content: `环境: ${config.ENV_NAME}\nAPI: ${config.API_BASE_URL}\n调试: ${config.DEBUG ? '开启' : '关闭'}`,
      showCancel: false
    })
  }
}

// 导出配置
export {
  ENV_TYPE,
  ENV_CONFIG,
  getCurrentEnv,
  getCurrentConfig,
  isDevelopment,
  isProduction,
  isDebugMode,
  getApiBaseUrl,
  getWsBaseUrl,
  getLogLevel,
  showEnvInfo
}

// 默认导出当前配置
export default getCurrentConfig()
