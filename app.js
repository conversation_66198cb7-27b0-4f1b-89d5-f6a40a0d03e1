// app.js
import { initState, refreshUser } from './utils/store.js'
import { isLoggedIn, autoRefreshToken } from './utils/auth.js'

App({
  onLaunch() {
    console.log('设备监控系统启动')

    // 初始化全局状态
    this.initApp()

    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)
  },

  onShow() {
    // 应用从后台进入前台时
    console.log('应用进入前台')

    // 自动刷新Token时间
    autoRefreshToken()

    // 如果已登录，刷新用户信息
    if (isLoggedIn()) {
      refreshUser().catch(error => {
        console.error('刷新用户信息失败:', error)
      })
    }
  },

  onHide() {
    // 应用从前台进入后台时
    console.log('应用进入后台')
  },

  /**
   * 初始化应用
   */
  async initApp() {
    try {
      // 初始化全局状态
      initState()

      // 检查登录状态
      if (isLoggedIn()) {
        console.log('用户已登录，刷新用户信息')
        await refreshUser()
      } else {
        console.log('用户未登录')
      }

    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  },

  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    // 系统信息
    systemInfo: null,
    // 网络状态
    networkType: 'unknown'
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    if (!this.globalData.systemInfo) {
      try {
        this.globalData.systemInfo = wx.getSystemInfoSync()
      } catch (error) {
        console.error('获取系统信息失败:', error)
      }
    }
    return this.globalData.systemInfo
  },

  /**
   * 监听网络状态变化
   */
  watchNetworkStatus() {
    wx.onNetworkStatusChange((res) => {
      this.globalData.networkType = res.networkType
      console.log('网络状态变化:', res)

      // 网络恢复时，如果已登录则刷新用户信息
      if (res.isConnected && isLoggedIn()) {
        refreshUser().catch(error => {
          console.error('网络恢复后刷新用户信息失败:', error)
        })
      }
    })
  }
})
