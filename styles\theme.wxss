/**
 * 设备监控系统 - 主题样式配置
 * 基于 Vant Weapp 原生风格增强
 * 创建时间: 2025-01-07
 */

/* ==================== CSS 变量定义 ==================== */
page {
  /* Vant 原生主题色彩 */
  --van-primary-color: #1989fa;
  --van-success-color: #07c160;
  --van-warning-color: #ff976a;
  --van-danger-color: #ee0a24;
  --van-info-color: #1989fa;

  /* 背景色彩 */
  --van-background-color: #f7f8fa;
  --van-background-2: #ffffff;
  --van-gray-1: #f7f8fa;
  --van-gray-2: #f2f3f5;
  --van-gray-3: #ebedf0;
  --van-gray-4: #dcdee0;
  --van-gray-5: #c8c9cc;
  --van-gray-6: #969799;
  --van-gray-7: #646566;
  --van-gray-8: #323233;

  /* 文字色彩 */
  --van-text-color: #323233;
  --van-text-color-2: #646566;
  --van-text-color-3: #969799;
  --van-text-link-color: #1989fa;

  /* 边框色彩 */
  --van-border-color: #ebedf0;
  --van-border-width-base: 1px;

  /* 设备监控专用色彩 */
  --device-online: #07c160;
  --device-offline: #c8c9cc;
  --device-warning: #ff976a;
  --device-error: #ee0a24;
  --device-maintenance: #1989fa;

  /* 状态色彩语义化 */
  --status-normal: #07c160;
  --status-warning: #ff976a;
  --status-danger: #ee0a24;
  --status-info: #1989fa;
  --status-disabled: #c8c9cc;

  /* 间距规范 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-xxl: 48rpx;

  /* 圆角规范 */
  --border-radius-sm: 4rpx;
  --border-radius-md: 8rpx;
  --border-radius-lg: 12rpx;
  --border-radius-xl: 16rpx;

  /* 字体规范 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;

  /* 行高规范 */
  --line-height-xs: 28rpx;
  --line-height-sm: 34rpx;
  --line-height-md: 40rpx;
  --line-height-lg: 44rpx;
  --line-height-xl: 50rpx;

  /* 阴影规范 */
  --shadow-light: 0 2rpx 12rpx rgba(100, 101, 102, 0.12);
  --shadow-base: 0 2rpx 8rpx rgba(100, 101, 102, 0.16);
  --shadow-dark: 0 4rpx 12rpx rgba(100, 101, 102, 0.24);

  /* 动画时长 */
  --animation-duration-fast: 0.2s;
  --animation-duration-base: 0.3s;
  --animation-duration-slow: 0.5s;
}

/* ==================== 全局重置样式 ==================== */
page {
  background-color: var(--van-background-color);
  color: var(--van-text-color);
  font-size: var(--font-size-md);
  line-height: var(--line-height-md);
}

/* 清除默认样式 */
view, text, image, button {
  box-sizing: border-box;
}

/* ==================== 通用工具类 ==================== */

/* 间距工具类 */
.m-xs { margin: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }

.mt-xs { margin-top: var(--spacing-xs) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }
.mt-xl { margin-top: var(--spacing-xl) !important; }

.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }

.ml-xs { margin-left: var(--spacing-xs) !important; }
.ml-sm { margin-left: var(--spacing-sm) !important; }
.ml-md { margin-left: var(--spacing-md) !important; }
.ml-lg { margin-left: var(--spacing-lg) !important; }
.ml-xl { margin-left: var(--spacing-xl) !important; }

.mr-xs { margin-right: var(--spacing-xs) !important; }
.mr-sm { margin-right: var(--spacing-sm) !important; }
.mr-md { margin-right: var(--spacing-md) !important; }
.mr-lg { margin-right: var(--spacing-lg) !important; }
.mr-xl { margin-right: var(--spacing-xl) !important; }

.p-xs { padding: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }

.pt-xs { padding-top: var(--spacing-xs) !important; }
.pt-sm { padding-top: var(--spacing-sm) !important; }
.pt-md { padding-top: var(--spacing-md) !important; }
.pt-lg { padding-top: var(--spacing-lg) !important; }
.pt-xl { padding-top: var(--spacing-xl) !important; }

.pb-xs { padding-bottom: var(--spacing-xs) !important; }
.pb-sm { padding-bottom: var(--spacing-sm) !important; }
.pb-md { padding-bottom: var(--spacing-md) !important; }
.pb-lg { padding-bottom: var(--spacing-lg) !important; }
.pb-xl { padding-bottom: var(--spacing-xl) !important; }

.pl-xs { padding-left: var(--spacing-xs) !important; }
.pl-sm { padding-left: var(--spacing-sm) !important; }
.pl-md { padding-left: var(--spacing-md) !important; }
.pl-lg { padding-left: var(--spacing-lg) !important; }
.pl-xl { padding-left: var(--spacing-xl) !important; }

.pr-xs { padding-right: var(--spacing-xs) !important; }
.pr-sm { padding-right: var(--spacing-sm) !important; }
.pr-md { padding-right: var(--spacing-md) !important; }
.pr-lg { padding-right: var(--spacing-lg) !important; }
.pr-xl { padding-right: var(--spacing-xl) !important; }

/* 文字工具类 */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-md { font-size: var(--font-size-md) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }

.text-primary { color: var(--van-primary-color) !important; }
.text-success { color: var(--van-success-color) !important; }
.text-warning { color: var(--van-warning-color) !important; }
.text-danger { color: var(--van-danger-color) !important; }
.text-info { color: var(--van-info-color) !important; }

.text-main { color: var(--van-text-color) !important; }
.text-sub { color: var(--van-text-color-2) !important; }
.text-placeholder { color: var(--van-text-color-3) !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.text-bold { font-weight: bold !important; }
.text-normal { font-weight: normal !important; }

/* 布局工具类 */
.flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

/* 显示隐藏 */
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.hidden { display: none !important; }

/* 圆角工具类 */
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: 50% !important; }

/* 阴影工具类 */
.shadow-light { box-shadow: var(--shadow-light) !important; }
.shadow-base { box-shadow: var(--shadow-base) !important; }
.shadow-dark { box-shadow: var(--shadow-dark) !important; }
.shadow-none { box-shadow: none !important; }
