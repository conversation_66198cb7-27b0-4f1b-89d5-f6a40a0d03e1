<!--pages/login/login.wxml-->
<view class="page-container">
  <!-- 顶部品牌区域 -->
  <view class="login-header">
    <view class="brand-logo">
      <van-icon name="setting" size="80rpx" color="#1989fa" />
    </view>
    <view class="brand-title">设备监控系统</view>
    <view class="brand-subtitle">专业的设备监控管理平台</view>
  </view>

  <!-- 登录内容区域 -->
  <view class="login-content">

    <!-- 系统账号登录 -->
    <view class="login-section">
      <view class="section-title">
        <text class="title-text">系统账号登录</text>
        <text class="title-desc">请输入您的系统账号信息</text>
      </view>

      <view class="login-form">
        <van-cell-group>
          <van-field
            value="{{username}}"
            placeholder="请输入用户名"
            border="{{false}}"
            bind:change="onUsernameChange"
            left-icon="manager"
            clearable
            required
          />
          <van-field
            value="{{password}}"
            type="password"
            placeholder="请输入密码"
            border="{{false}}"
            bind:change="onPasswordChange"
            left-icon="lock"
            clearable
            required
          />
        </van-cell-group>

        <view class="login-actions">
          <van-button
            type="primary"
            size="large"
            round
            loading="{{loginLoading}}"
            disabled="{{loginLoading || !canLogin}}"
            bind:click="handleSystemLogin"
            custom-class="login-btn"
          >
            {{loginLoading ? '登录中...' : '登录'}}
          </van-button>
        </view>

        <view class="login-tips">
          <text class="tip-text">登录后可选择绑定微信账户，实现快速登录</text>
        </view>
      </view>
    </view>

    <!-- 微信快速登录（仅已绑定用户显示） -->
    <view class="login-section" wx:if="{{showWxLogin}}">
      <view class="section-divider">
        <text class="divider-text">或</text>
      </view>

      <view class="wx-login-area">
        <van-button
          type="info"
          size="large"
          round
          loading="{{wxLoginLoading}}"
          disabled="{{wxLoginLoading}}"
          bind:click="handleWxLogin"
          custom-class="wx-login-btn"
        >
          <van-icon name="wechat" size="20" wx:if="{{!wxLoginLoading}}" />
          {{wxLoginLoading ? '登录中...' : '微信快速登录'}}
        </van-button>

        <view class="login-tips">
          <text class="tip-text">使用已绑定的微信账户快速登录</text>
        </view>
      </view>
    </view>

    <!-- 登录状态提示 -->
    <view class="login-status" wx:if="{{statusMessage}}">
      <view class="status-message {{statusType}}">
        <van-icon name="{{statusIcon}}" size="16" />
        <text class="status-text">{{statusMessage}}</text>
      </view>
    </view>

  </view>

  <!-- 底部信息 -->
  <view class="login-footer">
    <view class="footer-links">
      <text class="link-item" bind:tap="showPrivacyPolicy">隐私政策</text>
      <text class="link-divider">|</text>
      <text class="link-item" bind:tap="showUserAgreement">用户协议</text>
      <text class="link-divider">|</text>
      <text class="link-item" bind:tap="showNetworkDiagnostic">网络诊断</text>
    </view>
    <view class="footer-copyright">
      <text class="copyright-text">© 2025 设备监控系统</text>
    </view>
  </view>

  <!-- Toast组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog组件 -->
  <van-dialog id="van-dialog" />
</view>
