// pages/material/index.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../utils/permission.js'

Page({
  data: {
    // 子菜单选项
    menuItems: [
      {
        id: 'consumables-list',
        icon: 'shopping-cart-o',
        title: '消耗品清单',
        subtitle: '查看消耗品库存信息',
        color: '#07c160',
        page: '/pages/material/consumables/list'
      },
      {
        id: 'spareparts-list',
        icon: 'setting-o',
        title: '备品备件清单',
        subtitle: '查看备品备件库存信息',
        color: '#1989fa',
        page: '/pages/material/spareparts/list'
      },
      {
        id: 'inbound-management',
        icon: 'add-square',
        title: '物品入库',
        subtitle: '物品入库单管理',
        color: '#ff976a',
        page: '/pages/material/inbound/list'
      }
    ]
  },

  onLoad() {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 页面初始化完成
    console.log('物资管理子菜单初始化完成')
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      Toast.success('数据已更新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      Toast.fail('刷新失败')
    }
  },

  /**
   * 菜单项点击
   */
  onMenuItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击菜单项:', item)

    if (item.page) {
      wx.navigateTo({
        url: item.page,
        fail: () => {
          Toast.fail(`${item.title}功能开发中`)
        }
      })
    } else {
      Toast.fail(`${item.title}功能开发中`)
    }
  }
})
