// pages/material/inbound/add.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { addInbound, getItemSelectList } from '../../../api/material.js'

Page({
  data: {
    // 表单数据
    formData: {
      businessDate: '',
      supplierName: '',
      inboundType: 1,
      inboundDescription: ''
    },

    // 入库明细列表
    detailList: [],

    // 入库类型选项
    inboundTypeOptions: [
      { text: '采购入库', value: 1 },
      { text: '退货入库', value: 2 },
      { text: '调拨入库', value: 3 },
      { text: '其他入库', value: 4 }
    ],

    // 物品选择相关
    showItemPicker: false,
    itemList: [],
    itemSearchKeyword: '',
    itemLoading: false,

    // 仓库选择相关
    showWarehousePicker: false,
    warehouseList: [
      { warehouseId: 1, warehouseName: '主仓库' },
      { warehouseId: 2, warehouseName: '备用仓库' }
    ],

    // 日期选择
    showDatePicker: false,
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date(2030, 11, 31).getTime(),

    // 入库类型选择
    showTypePicker: false,

    // 页面状态
    submitting: false
  },

  onLoad() {
    console.log('📋 新增入库单页面加载')

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 设置默认业务日期为今天
    const today = new Date()
    const dateStr = today.getFullYear() + '-' +
                   String(today.getMonth() + 1).padStart(2, '0') + '-' +
                   String(today.getDate()).padStart(2, '0')

    this.setData({
      'formData.businessDate': dateStr
    })

    console.log('✅ 页面数据初始化完成')
  },

  /**
   * 表单字段变化处理
   */
  onFieldChange(event) {
    const { field } = event.currentTarget.dataset
    const value = event.detail

    this.setData({
      [`formData.${field}`]: value
    })

    console.log(`📝 字段 ${field} 更新为:`, value)
  },

  /**
   * 显示日期选择器
   */
  onShowDatePicker() {
    this.setData({ showDatePicker: true })
  },

  /**
   * 日期选择确认
   */
  onDateConfirm(event) {
    const date = new Date(event.detail)
    const dateStr = date.getFullYear() + '-' +
                   String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0')

    this.setData({
      'formData.businessDate': dateStr,
      showDatePicker: false
    })
  },

  /**
   * 日期选择取消
   */
  onDateCancel() {
    this.setData({ showDatePicker: false })
  },

  /**
   * 显示入库类型选择器
   */
  onShowTypePicker() {
    this.setData({ showTypePicker: true })
  },

  /**
   * 入库类型选择确认
   */
  onTypeConfirm(event) {
    const { value } = event.detail
    this.setData({
      'formData.inboundType': value,
      showTypePicker: false
    })
  },

  /**
   * 入库类型选择取消
   */
  onTypeCancel() {
    this.setData({ showTypePicker: false })
  },

  /**
   * 添加入库明细
   */
  onAddDetail() {
    this.loadItemList()
    this.setData({ showItemPicker: true })
  },

  /**
   * 加载物品列表
   */
  async loadItemList() {
    if (this.data.itemLoading) return

    this.setData({ itemLoading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: 1,
        pageSize: 50
      }

      // 添加搜索关键词
      if (this.data.itemSearchKeyword) {
        params.itemName = this.data.itemSearchKeyword
        params.itemCode = this.data.itemSearchKeyword
      }

      console.log('🔍 查询物品列表参数:', params)

      // 调用API获取物品列表
      const response = await getItemSelectList(params)

      if (response && response.code === 200 && response.data) {
        const itemList = response.data.records || []
        console.log('📦 获取到物品列表:', itemList)

        this.setData({ itemList })
      } else {
        throw new Error(response?.msg || '获取物品列表失败')
      }
    } catch (error) {
      console.error('❌ 加载物品列表失败:', error)
      Toast.fail('获取物品列表失败')
    } finally {
      this.setData({ itemLoading: false })
    }
  },

  /**
   * 物品搜索输入
   */
  onItemSearchInput(event) {
    this.setData({
      itemSearchKeyword: event.detail
    })
  },

  /**
   * 物品搜索
   */
  onItemSearch() {
    this.loadItemList()
  },

  /**
   * 物品选择
   */
  onItemSelect(event) {
    const item = event.currentTarget.dataset.item
    console.log('选择物品:', item)

    // 关闭物品选择器
    this.setData({
      showItemPicker: false,
      showWarehousePicker: true,
      currentSelectedItem: item
    })
  },

  /**
   * 仓库选择
   */
  onWarehouseSelect(event) {
    const warehouse = event.currentTarget.dataset.warehouse
    console.log('选择仓库:', warehouse)

    const item = this.data.currentSelectedItem

    // 创建新的明细项
    const newDetail = {
      itemId: item.itemId,
      itemName: item.itemName,
      itemCode: item.itemCode,
      specModel: item.specModel,
      unit: item.unit,
      warehouseId: warehouse.warehouseId,
      warehouseName: warehouse.warehouseName,
      quantity: 1, // 默认数量为1
      unitPrice: 0,
      amount: 0,
      batchNo: '',
      productionDate: '',
      expiryDate: '',
      shelfLocation: '',
      remark: ''
    }

    // 添加到明细列表
    const detailList = [...this.data.detailList, newDetail]

    this.setData({
      detailList,
      showWarehousePicker: false
    })

    Toast.success('已添加物品')
  },

  /**
   * 关闭物品选择器
   */
  onCloseItemPicker() {
    this.setData({ showItemPicker: false })
  },

  /**
   * 关闭仓库选择器
   */
  onCloseWarehousePicker() {
    this.setData({ showWarehousePicker: false })
  },

  /**
   * 更新明细项
   */
  onDetailFieldChange(event) {
    const { field, index } = event.currentTarget.dataset
    const value = event.detail

    // 更新指定字段
    this.setData({
      [`detailList[${index}].${field}`]: value
    })

    // 如果更新的是数量或单价，重新计算金额
    if (field === 'quantity' || field === 'unitPrice') {
      const detail = this.data.detailList[index]
      const quantity = field === 'quantity' ? parseFloat(value) : parseFloat(detail.quantity)
      const unitPrice = field === 'unitPrice' ? parseFloat(value) : parseFloat(detail.unitPrice)

      // 计算金额
      const amount = (quantity * unitPrice).toFixed(2)

      this.setData({
        [`detailList[${index}].amount`]: amount
      })
    }
  },

  /**
   * 删除明细项
   */
  onDeleteDetail(event) {
    const { index } = event.currentTarget.dataset

    Dialog.confirm({
      title: '删除确认',
      message: '确定要删除该物品吗？',
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    }).then(() => {
      // 从明细列表中移除
      const detailList = [...this.data.detailList]
      detailList.splice(index, 1)

      this.setData({ detailList })
      Toast.success('已删除')
    }).catch(() => {
      // 取消删除
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData, detailList } = this.data

    // 验证基本信息
    if (!formData.businessDate) {
      Toast.fail('请选择业务日期')
      return false
    }

    if (!formData.supplierName || !formData.supplierName.trim()) {
      Toast.fail('请输入供应商名称')
      return false
    }

    if (!formData.inboundType) {
      Toast.fail('请选择入库类型')
      return false
    }

    // 验证明细列表
    if (!detailList || detailList.length === 0) {
      Toast.fail('请至少添加一个入库物品')
      return false
    }

    // 验证每个明细项
    for (let i = 0; i < detailList.length; i++) {
      const detail = detailList[i]

      if (!detail.quantity || parseFloat(detail.quantity) <= 0) {
        Toast.fail(`第${i + 1}个物品的数量必须大于0`)
        return false
      }

      if (!detail.warehouseId) {
        Toast.fail(`第${i + 1}个物品未选择仓库`)
        return false
      }
    }

    return true
  },

  /**
   * 保存入库单
   */
  async onSave() {
    if (!this.validateForm()) {
      return
    }

    if (this.data.submitting) {
      return
    }

    this.setData({ submitting: true })

    try {
      // 构建入库单数据
      const inboundData = {
        main: {
          businessDate: this.data.formData.businessDate,
          supplierName: this.data.formData.supplierName.trim(),
          inboundType: this.data.formData.inboundType,
          inboundDescription: this.data.formData.inboundDescription.trim()
        },
        details: this.data.detailList.map(detail => ({
          itemId: detail.itemId,
          batchNo: detail.batchNo,
          productionDate: detail.productionDate || null,
          expiryDate: detail.expiryDate || null,
          quantity: parseFloat(detail.quantity),
          unitPrice: parseFloat(detail.unitPrice) || 0,
          amount: parseFloat(detail.amount) || 0,
          warehouseId: detail.warehouseId,
          shelfLocation: detail.shelfLocation,
          remark: detail.remark
        }))
      }

      console.log('💾 保存入库单数据:', inboundData)

      // 调用API保存入库单
      const response = await addInbound(inboundData)

      if (response && response.code === 200) {
        Toast.success('入库单保存成功')

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(response?.msg || '保存失败')
      }
    } catch (error) {
      console.error('❌ 保存入库单失败:', error)
      Toast.fail(error.message || '保存失败')
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 取消操作
   */
  onCancel() {
    if (this.data.detailList.length > 0 ||
        this.data.formData.supplierName ||
        this.data.formData.inboundDescription) {
      Dialog.confirm({
        title: '取消确认',
        message: '当前有未保存的数据，确定要取消吗？',
        confirmButtonText: '确定取消',
        cancelButtonText: '继续编辑'
      }).then(() => {
        wx.navigateBack()
      }).catch(() => {
        // 继续编辑
      })
    } else {
      wx.navigateBack()
    }
  }
})
