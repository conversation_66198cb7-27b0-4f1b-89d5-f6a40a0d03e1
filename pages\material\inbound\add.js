// pages/material/inbound/add.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'

Page({
  data: {
    
  },

  onLoad() {
    console.log('📋 新增入库单页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    Toast.success('新增入库单功能开发中')
  }
})
