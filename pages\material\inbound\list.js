// pages/material/inbound/list.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { getInboundList, deleteInbound } from '../../../api/material.js'

Page({
  data: {
    // 搜索条件
    searchKeyword: '',
    statusFilter: '', // 状态筛选
    
    // 入库单列表
    inboundList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 10,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 状态选项
    statusOptions: [
      { text: '全部状态', value: '' },
      { text: '草稿', value: '1' },
      { text: '待确认', value: '2' },
      { text: '待审核', value: '3' },
      { text: '已通过', value: '4' },
      { text: '已退回', value: '5' }
    ],
    
    // 筛选器显示状态
    showStatusFilter: false
  },

  onLoad() {
    console.log('📋 入库单列表页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('📋 初始化入库单列表页面')
    this.loadInboundList()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      'pageInfo.current': 1,
      inboundList: [],
      finished: false
    })
    await this.loadInboundList()
  },

  /**
   * 下拉刷新
   */
  async onRefresh() {
    this.setData({ refreshing: true })
    await this.refreshData()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  async onLoadMore() {
    if (this.data.finished || this.data.loading) {
      return
    }

    const { current, pages } = this.data.pageInfo
    if (current >= pages) {
      this.setData({ finished: true })
      return
    }

    this.setData({
      'pageInfo.current': current + 1
    })
    await this.loadInboundList(false)
  },

  /**
   * 加载入库单列表
   */
  async loadInboundList(reset = true) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.trim()
        // 可以按入库单ID或供应商名称搜索
        params.inboundId = keyword
        params.supplierName = keyword
      }

      // 添加状态筛选
      if (this.data.statusFilter) {
        params.status = parseInt(this.data.statusFilter)
      }

      console.log('📋 入库单查询参数:', params)

      // 调用后端接口获取入库单数据
      const response = await getInboundList(params)
      
      if (response && response.code === 200 && response.data) {
        const { records, total, size, current, pages } = response.data
        
        // 处理数据格式，确保与界面显示一致
        const processedRecords = records.map(item => ({
          inboundId: item.inboundId,
          businessDate: item.businessDate,
          supplierName: item.supplierName || '未指定',
          inboundType: item.inboundType,
          inboundTypeName: this.getInboundTypeName(item.inboundType),
          status: item.status,
          statusName: this.getStatusName(item.status),
          totalAmount: item.totalAmount || 0,
          itemCount: item.itemCount || 0,
          creatorName: item.creatorName || '未知',
          handlerName: item.handlerName || '',
          auditorName: item.auditorName || '',
          createTime: item.createTime,
          remark: item.remark || ''
        }))

        const newList = reset ? processedRecords : [...this.data.inboundList, ...processedRecords]
        
        this.setData({
          inboundList: newList,
          pageInfo: {
            current: current,
            size: size,
            total: total,
            pages: pages
          },
          finished: current >= pages
        })

        console.log('✅ 入库单列表加载成功:', {
          total: total,
          current: current,
          pages: pages,
          records: processedRecords.length
        })

      } else {
        throw new Error(response?.msg || '获取数据失败')
      }

    } catch (error) {
      console.error('❌ 加载入库单列表失败:', error)
      
      // 如果是第一次加载失败，显示模拟数据作为备用
      if (reset && this.data.inboundList.length === 0) {
        console.log('🔄 网络异常，使用模拟数据作为备用')
        const mockData = this.getMockInboundData()
        const newList = mockData.records
        
        this.setData({
          inboundList: newList,
          pageInfo: {
            current: mockData.current,
            size: mockData.size,
            total: mockData.total,
            pages: mockData.pages
          },
          finished: mockData.current >= mockData.pages
        })
        
        Toast.fail('网络异常，显示离线数据')
      } else {
        Toast.fail(error.message || '加载失败')
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 获取入库类型名称
   */
  getInboundTypeName(type) {
    const typeMap = {
      1: '采购入库',
      2: '退货入库',
      3: '调拨入库',
      4: '其他入库'
    }
    return typeMap[type] || '未知类型'
  },

  /**
   * 获取状态名称
   */
  getStatusName(status) {
    const statusMap = {
      1: '草稿',
      2: '待确认',
      3: '待审核',
      4: '已通过',
      5: '已退回'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取模拟入库单数据
   */
  getMockInboundData() {
    const mockItems = [
      {
        inboundId: 'RK202501100001',
        businessDate: '2025-01-10',
        supplierName: '北京科技有限公司',
        inboundType: 1,
        inboundTypeName: '采购入库',
        status: 2,
        statusName: '待确认',
        totalAmount: 15600.00,
        itemCount: 5,
        creatorName: '张三',
        handlerName: '',
        auditorName: '',
        createTime: '2025-01-10 09:30:00',
        remark: '月度采购入库'
      },
      {
        inboundId: 'RK202501100002',
        businessDate: '2025-01-10',
        supplierName: '上海机械设备厂',
        inboundType: 1,
        inboundTypeName: '采购入库',
        status: 4,
        statusName: '已通过',
        totalAmount: 8900.00,
        itemCount: 3,
        creatorName: '李四',
        handlerName: '王五',
        auditorName: '赵六',
        createTime: '2025-01-10 08:15:00',
        remark: '备件采购'
      },
      {
        inboundId: 'RK202501090001',
        businessDate: '2025-01-09',
        supplierName: '广州化工材料公司',
        inboundType: 4,
        inboundTypeName: '其他入库',
        status: 1,
        statusName: '草稿',
        totalAmount: 3200.00,
        itemCount: 2,
        creatorName: '张三',
        handlerName: '',
        auditorName: '',
        createTime: '2025-01-09 16:45:00',
        remark: '临时入库'
      }
    ]

    return {
      records: mockItems,
      total: mockItems.length,
      size: 10,
      current: 1,
      pages: 1
    }
  },

  /**
   * 搜索输入 - 使用节流优化
   */
  onSearchInput(event) {
    const keyword = event.detail
    this.setData({
      searchKeyword: keyword
    })
    
    // 清除之前的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    
    // 设置新的定时器，300ms后执行搜索
    this.searchTimer = setTimeout(() => {
      this.performSearch(keyword)
    }, 300)
  },

  /**
   * 搜索按钮点击
   */
  onSearch() {
    this.performSearch(this.data.searchKeyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行入库单搜索，关键词:', keyword)
    
    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      inboundList: [],
      finished: false
    })
    
    // 重新加载数据
    this.loadInboundList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 显示状态筛选器
   */
  onShowStatusFilter() {
    this.setData({
      showStatusFilter: true
    })
  },

  /**
   * 状态筛选确认
   */
  onStatusFilterConfirm(event) {
    const { value } = event.detail
    this.setData({
      statusFilter: value,
      showStatusFilter: false
    })
    
    console.log('📋 状态筛选:', value)
    this.refreshData()
  },

  /**
   * 状态筛选取消
   */
  onStatusFilterCancel() {
    this.setData({
      showStatusFilter: false
    })
  },

  /**
   * 入库单项点击
   */
  onInboundItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击入库单项:', item)
    
    // 跳转到入库单详情页面
    wx.navigateTo({
      url: `/pages/material/inbound/detail?inboundId=${item.inboundId}`,
      fail: () => {
        Toast.fail('入库单详情功能开发中')
      }
    })
  },

  /**
   * 新增入库单
   */
  onAddInbound() {
    console.log('新增入库单')
    
    // 跳转到新增入库单页面
    wx.navigateTo({
      url: '/pages/material/inbound/add',
      fail: () => {
        Toast.fail('新增入库单功能开发中')
      }
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
