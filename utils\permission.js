/**
 * 设备监控系统 - 路由权限控制
 * 页面访问权限验证和路由守卫
 * 创建时间: 2025-01-07
 */

import { isLoggedIn, needBindAccount } from './auth.js'

// 白名单页面（无需登录即可访问）
const WHITE_LIST = [
  '/pages/login/login'
]

// 需要登录的页面
const AUTH_REQUIRED_PAGES = [
  '/pages/index/index',
  '/pages/logs/logs'
]

/**
 * 检查页面是否在白名单中
 */
function isInWhiteList(url) {
  return WHITE_LIST.some(path => url.includes(path))
}

/**
 * 检查页面是否需要登录
 */
function isAuthRequired(url) {
  return AUTH_REQUIRED_PAGES.some(path => url.includes(path))
}

/**
 * 路由守卫 - 页面跳转前检查
 * 在页面的 onLoad 中调用
 */
function checkPagePermission(url) {
  console.log('检查页面权限:', url)
  
  // 白名单页面直接通过
  if (isInWhiteList(url)) {
    return true
  }
  
  // 检查是否需要登录
  if (isAuthRequired(url)) {
    if (!isLoggedIn()) {
      // 未登录，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return false
    }
    
    // 检查是否需要绑定账号
    if (needBindAccount()) {
      // 需要绑定账号，跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return false
    }
  }
  
  return true
}

/**
 * 页面权限Mixin
 * 在需要权限控制的页面中使用
 */
function createPermissionMixin() {
  return {
    onLoad(options) {
      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentUrl = '/' + currentPage.route
      
      // 检查页面权限
      if (!checkPagePermission(currentUrl)) {
        return
      }
      
      // 调用原始的 onLoad 方法
      if (this._originalOnLoad) {
        this._originalOnLoad.call(this, options)
      }
    }
  }
}

/**
 * 导航守卫 - 拦截导航操作
 */
function setupNavigationGuard() {
  // 保存原始的导航方法
  const originalNavigateTo = wx.navigateTo
  const originalRedirectTo = wx.redirectTo
  const originalReLaunch = wx.reLaunch
  const originalSwitchTab = wx.switchTab
  
  // 重写 navigateTo
  wx.navigateTo = function(options) {
    const { url } = options
    
    if (isInWhiteList(url)) {
      return originalNavigateTo.call(this, options)
    }
    
    if (isAuthRequired(url) && !isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      })
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
      return
    }
    
    return originalNavigateTo.call(this, options)
  }
  
  // 重写 redirectTo
  wx.redirectTo = function(options) {
    const { url } = options
    
    if (isInWhiteList(url)) {
      return originalRedirectTo.call(this, options)
    }
    
    if (isAuthRequired(url) && !isLoggedIn()) {
      return originalRedirectTo.call(this, {
        url: '/pages/login/login'
      })
    }
    
    return originalRedirectTo.call(this, options)
  }
  
  // 重写 reLaunch
  wx.reLaunch = function(options) {
    const { url } = options
    
    if (isInWhiteList(url)) {
      return originalReLaunch.call(this, options)
    }
    
    if (isAuthRequired(url) && !isLoggedIn()) {
      return originalReLaunch.call(this, {
        url: '/pages/login/login'
      })
    }
    
    return originalReLaunch.call(this, options)
  }
  
  // 重写 switchTab
  wx.switchTab = function(options) {
    const { url } = options
    
    if (isAuthRequired(url) && !isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'error'
      })
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
      return
    }
    
    return originalSwitchTab.call(this, options)
  }
}

/**
 * 检查特定权限
 */
function hasPermission(permission) {
  // 这里可以从全局状态或缓存中获取用户权限
  // 暂时返回 true，后续可以完善
  return true
}

/**
 * 检查特定角色
 */
function hasRole(role) {
  // 这里可以从全局状态或缓存中获取用户角色
  // 暂时返回 true，后续可以完善
  return true
}

/**
 * 权限指令 - 用于控制页面元素显示
 */
function checkElementPermission(permission) {
  if (!isLoggedIn()) {
    return false
  }
  
  return hasPermission(permission)
}

/**
 * 角色指令 - 用于控制页面元素显示
 */
function checkElementRole(role) {
  if (!isLoggedIn()) {
    return false
  }
  
  return hasRole(role)
}

// 导出方法
export {
  // 基础权限检查
  isInWhiteList,
  isAuthRequired,
  checkPagePermission,
  
  // 权限和角色检查
  hasPermission,
  hasRole,
  checkElementPermission,
  checkElementRole,
  
  // Mixin和守卫
  createPermissionMixin,
  setupNavigationGuard,
  
  // 常量
  WHITE_LIST,
  AUTH_REQUIRED_PAGES
}
