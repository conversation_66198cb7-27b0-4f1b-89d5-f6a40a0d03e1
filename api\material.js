/**
 * 物资管理系统 - 物资相关API
 * 对接后端物资模块接口
 * 创建时间: 2025-01-10
 */

import { post, get, put, del } from '../utils/request.js'

/**
 * 获取物品列表
 * 对接后端 POST /item/list 接口
 * @param {object} params - 查询参数
 * @param {number} params.pageNum - 页码（必填）
 * @param {number} params.pageSize - 每页数量（必填）
 * @param {string} params.itemId - 物品ID（可选）
 * @param {string} params.itemName - 物品名称（模糊查询）
 * @param {string} params.itemCode - 物品编码（模糊查询）
 * @param {number} params.itemType - 物品类型：1-消耗品, 2-备品备件
 * @param {string} params.specModel - 规格型号（模糊查询）
 * @param {number} params.stockStatus - 库存状态：1-正常, 2-不足, 3-过剩
 * @param {number} params.warehouseId - 仓库ID
 * @param {number} params.hasExpiry - 是否有有效期：0-无, 1-有
 * @returns {Promise}
 */
export function getItemList(params = {}) {
  console.log('🚀 发起获取物品列表请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    ...params
  }

  return post('/item/list', defaultParams, {
    showLoading: false, // 库存清单页面有自己的加载状态
    showError: false   // 由页面处理错误
  }).then(response => {
    console.log('📥 物品列表原始响应:', response)

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 情况1: RuoYi标准格式 {code: 200, msg: "操作成功", data: {...}}
      if (response.code === 200 && response.data) {
        console.log('✅ 标准RuoYi格式响应')
        return response
      }
      // 情况2: 直接返回分页数据 {records: [], total: number, ...}
      else if (response.records && Array.isArray(response.records)) {
        console.log('✅ 直接分页数据格式响应')
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      }
      // 情况3: 其他错误格式
      else {
        console.error('❌ 未知的响应格式:', response)
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      console.error('❌ 无效的响应数据:', response)
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取物品列表失败:', error)
    throw error
  })
}

/**
 * 获取物品选择列表
 * 对接后端 POST /item/select/list 接口
 * @param {object} params - 查询参数
 * @returns {Promise}
 */
export function getItemSelectList(params = {}) {
  console.log('🚀 发起获取物品选择列表请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    ...params
  }

  return post('/item/select/list', defaultParams, {
    showLoading: false,
    showError: false
  }).then(response => {
    console.log('📥 物品选择列表原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.records && Array.isArray(response.records)) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取物品选择列表失败:', error)
    throw error
  })
}

/**
 * 获取物品详情
 * 对接后端 GET /item/{itemId} 接口
 * @param {string} itemId - 物品ID
 * @returns {Promise}
 */
export function getItemDetail(itemId) {
  console.log('🚀 发起获取物品详情请求:', itemId)

  if (!itemId) {
    return Promise.reject(new Error('物品ID不能为空'))
  }

  return get(`/item/${itemId}`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 物品详情原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.itemId || response.itemName) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取物品详情失败:', error)
    throw error
  })
}

/**
 * 获取库存统计
 * 对接后端 POST /item/stock/statistics 接口
 * @param {object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {number} params.statisticsType - 统计类型：1-按仓库统计, 2-按物品统计
 * @param {string} params.itemId - 物品ID
 * @param {string} params.itemName - 物品名称（模糊查询）
 * @param {string} params.itemCode - 物品编码（模糊查询）
 * @param {number} params.itemType - 物品类型
 * @param {string} params.specModel - 规格型号（模糊查询）
 * @param {number} params.warehouseId - 仓库ID
 * @param {number} params.stockStatus - 库存状态
 * @param {boolean} params.onlyWithStock - 是否只显示有库存的记录
 * @returns {Promise}
 */
export function getStockStatistics(params = {}) {
  console.log('🚀 发起获取库存统计请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    statisticsType: 2, // 默认按物品统计
    onlyWithStock: true, // 默认只显示有库存的记录
    ...params
  }

  return post('/item/stock/statistics', defaultParams, {
    showLoading: false,
    showError: false
  }).then(response => {
    console.log('📥 库存统计原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.records && Array.isArray(response.records)) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取库存统计失败:', error)
    throw error
  })
}

/**
 * 获取库存视图
 * 对接后端 POST /item/stock/view 接口
 * @param {object} params - 查询参数
 * @param {number} params.pageNum - 页码（默认1）
 * @param {number} params.pageSize - 每页大小（默认10）
 * @param {number} params.viewMode - 展示模式：1-按仓库展示, 2-按物品展示（必填）
 * @param {string} params.itemId - 物品ID
 * @param {string} params.itemName - 物品名称（模糊查询）
 * @param {string} params.itemCode - 物品编码（模糊查询）
 * @param {number} params.itemType - 物品类型
 * @param {string} params.specModel - 规格型号（模糊查询）
 * @param {number} params.warehouseId - 仓库ID
 * @param {number} params.stockStatus - 库存状态
 * @param {boolean} params.onlyWithStock - 是否只显示有库存的记录（默认true）
 * @returns {Promise}
 */
export function getStockView(params = {}) {
  console.log('🚀 发起获取库存视图请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    viewMode: 2, // 默认按物品展示
    onlyWithStock: true, // 默认只显示有库存的记录
    ...params
  }

  return post('/item/stock/view', defaultParams, {
    showLoading: false,
    showError: false
  }).then(response => {
    console.log('📥 库存视图原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.records && Array.isArray(response.records)) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取库存视图失败:', error)
    throw error
  })
}

/**
 * 获取入库单列表
 * 对接后端 POST /item/inbound/list 接口
 * @param {object} params - 查询参数
 * @param {number} params.pageNum - 页码（必填）
 * @param {number} params.pageSize - 每页数量（必填）
 * @param {string} params.inboundId - 入库单ID
 * @param {string} params.businessDate - 业务日期
 * @param {string} params.supplierName - 供应商名称
 * @param {number} params.inboundType - 入库类型：1-采购入库, 2-退货入库, 3-调拨入库, 4-其他入库
 * @param {number} params.status - 状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回
 * @param {string} params.creatorId - 制单人ID
 * @param {string} params.handlerId - 经手人ID
 * @param {string} params.auditorId - 审核人ID
 * @returns {Promise}
 */
export function getInboundList(params = {}) {
  console.log('🚀 发起获取入库单列表请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    ...params
  }

  return post('/item/inbound/list', defaultParams, {
    showLoading: false,
    showError: false
  }).then(response => {
    console.log('📥 入库单列表原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.records && Array.isArray(response.records)) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取入库单列表失败:', error)
    throw error
  })
}

/**
 * 获取入库单详情
 * 对接后端 GET /item/inbound/{inboundId} 接口
 * @param {string} inboundId - 入库单ID
 * @returns {Promise}
 */
export function getInboundDetail(inboundId) {
  console.log('🚀 发起获取入库单详情请求:', inboundId)

  if (!inboundId) {
    return Promise.reject(new Error('入库单ID不能为空'))
  }

  return get(`/item/inbound/${inboundId}`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 入库单详情原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.inboundId || response.businessDate) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取入库单详情失败:', error)
    throw error
  })
}

/**
 * 新增入库单
 * 对接后端 POST /item/inbound/add 接口
 * @param {object} inboundData - 入库单数据
 * @param {object} inboundData.main - 入库单主信息
 * @param {array} inboundData.details - 入库单明细列表
 * @returns {Promise}
 */
export function addInbound(inboundData) {
  console.log('🚀 发起新增入库单请求:', inboundData)

  if (!inboundData || !inboundData.main || !inboundData.details || inboundData.details.length === 0) {
    return Promise.reject(new Error('入库单数据不完整'))
  }

  return post('/item/inbound/add', inboundData, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 新增入库单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '新增入库单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 新增入库单失败:', error)
    throw error
  })
}

/**
 * 修改入库单
 * 对接后端 PUT /item/inbound/{inboundId} 接口
 * @param {string} inboundId - 入库单ID
 * @param {object} inboundData - 入库单数据
 * @returns {Promise}
 */
export function updateInbound(inboundId, inboundData) {
  console.log('🚀 发起修改入库单请求:', inboundId, inboundData)

  if (!inboundId) {
    return Promise.reject(new Error('入库单ID不能为空'))
  }

  return put(`/item/inbound/${inboundId}`, inboundData, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 修改入库单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '修改入库单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 修改入库单失败:', error)
    throw error
  })
}

/**
 * 提交入库单
 * 对接后端 PUT /item/inbound/submit/{inboundId} 接口
 * @param {string} inboundId - 入库单ID
 * @returns {Promise}
 */
export function submitInbound(inboundId) {
  console.log('🚀 发起提交入库单请求:', inboundId)

  if (!inboundId) {
    return Promise.reject(new Error('入库单ID不能为空'))
  }

  return put(`/item/inbound/submit/${inboundId}`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 提交入库单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '提交入库单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 提交入库单失败:', error)
    throw error
  })
}

/**
 * 确认入库单
 * 对接后端 PUT /item/inbound/handle/{inboundId} 接口
 * @param {string} inboundId - 入库单ID
 * @param {string} remark - 确认备注（可选）
 * @returns {Promise}
 */
export function handleInbound(inboundId, remark = '') {
  console.log('🚀 发起确认入库单请求:', inboundId, remark)

  if (!inboundId) {
    return Promise.reject(new Error('入库单ID不能为空'))
  }

  const params = remark ? { remark } : {}

  return put(`/item/inbound/handle/${inboundId}`, params, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 确认入库单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '确认入库单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 确认入库单失败:', error)
    throw error
  })
}

/**
 * 审核入库单
 * 对接后端 PUT /item/inbound/audit/{inboundId} 接口
 * @param {string} inboundId - 入库单ID
 * @param {number} status - 审核结果：4-通过, 5-退回
 * @param {string} remark - 审核备注（可选）
 * @returns {Promise}
 */
export function auditInbound(inboundId, status, remark = '') {
  console.log('🚀 发起审核入库单请求:', inboundId, status, remark)

  if (!inboundId) {
    return Promise.reject(new Error('入库单ID不能为空'))
  }

  if (![4, 5].includes(status)) {
    return Promise.reject(new Error('审核状态无效'))
  }

  const params = { status }
  if (remark) {
    params.remark = remark
  }

  return put(`/item/inbound/audit/${inboundId}`, params, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 审核入库单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '审核入库单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 审核入库单失败:', error)
    throw error
  })
}

/**
 * 删除入库单
 * 对接后端 DELETE /item/inbound/{inboundIds} 接口
 * @param {string|array} inboundIds - 入库单ID，多个用逗号分隔或数组
 * @returns {Promise}
 */
export function deleteInbound(inboundIds) {
  console.log('🚀 发起删除入库单请求:', inboundIds)

  if (!inboundIds) {
    return Promise.reject(new Error('入库单ID不能为空'))
  }

  const ids = Array.isArray(inboundIds) ? inboundIds.join(',') : inboundIds

  return del(`/item/inbound/${ids}`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 删除入库单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '删除入库单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 删除入库单失败:', error)
    throw error
  })
}

// ==================== 领用单相关接口 ====================

/**
 * 获取领用单列表
 * 对接后端 POST /item/requisition/list 接口
 * @param {object} params - 查询参数
 * @param {number} params.pageNum - 页码（必填）
 * @param {number} params.pageSize - 每页数量（必填）
 * @param {string} params.requisitionId - 领用单ID
 * @param {string} params.businessDate - 业务日期
 * @param {number} params.applicantId - 申请人ID
 * @param {number} params.deptId - 申请部门ID
 * @param {string} params.requisitionPurpose - 领用用途
 * @param {number} params.status - 状态：1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回, 6-已完成
 * @param {number} params.creatorId - 制单人ID
 * @param {number} params.handlerId - 经手人ID
 * @param {number} params.auditorId - 审核人ID
 * @returns {Promise}
 */
export function getRequisitionList(params = {}) {
  console.log('🚀 发起获取领用单列表请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    ...params
  }

  return post('/item/requisition/list', defaultParams, {
    showLoading: false,
    showError: false
  }).then(response => {
    console.log('📥 领用单列表原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.records && Array.isArray(response.records)) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取领用单列表失败:', error)
    throw error
  })
}

/**
 * 获取领用单详情
 * 对接后端 GET /item/requisition/{requisitionId} 接口
 * @param {string} requisitionId - 领用单ID
 * @returns {Promise}
 */
export function getRequisitionDetail(requisitionId) {
  console.log('🚀 发起获取领用单详情请求:', requisitionId)

  if (!requisitionId) {
    return Promise.reject(new Error('领用单ID不能为空'))
  }

  return get(`/item/requisition/${requisitionId}`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 领用单详情原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200 && response.data) {
        return response
      } else if (response.requisitionId || response.businessDate) {
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      } else {
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取领用单详情失败:', error)
    throw error
  })
}

/**
 * 新增领用单
 * 对接后端 POST /item/requisition/add 接口
 * @param {object} requisitionData - 领用单数据
 * @param {object} requisitionData.main - 领用单主信息
 * @param {array} requisitionData.details - 领用单明细列表
 * @returns {Promise}
 */
export function addRequisition(requisitionData) {
  console.log('🚀 发起新增领用单请求:', requisitionData)

  if (!requisitionData || !requisitionData.main || !requisitionData.details || requisitionData.details.length === 0) {
    return Promise.reject(new Error('领用单数据不完整'))
  }

  return post('/item/requisition/add', requisitionData, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 新增领用单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '新增领用单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 新增领用单失败:', error)
    throw error
  })
}

/**
 * 修改领用单
 * 对接后端 PUT /item/requisition/{requisitionId} 接口
 * @param {string} requisitionId - 领用单ID
 * @param {object} requisitionData - 领用单数据
 * @returns {Promise}
 */
export function updateRequisition(requisitionId, requisitionData) {
  console.log('🚀 发起修改领用单请求:', requisitionId, requisitionData)

  if (!requisitionId) {
    return Promise.reject(new Error('领用单ID不能为空'))
  }

  return put(`/item/requisition/${requisitionId}`, requisitionData, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 修改领用单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '修改领用单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 修改领用单失败:', error)
    throw error
  })
}

/**
 * 提交领用单
 * 对接后端 PUT /item/requisition/{requisitionId}/submit 接口
 * @param {string} requisitionId - 领用单ID
 * @returns {Promise}
 */
export function submitRequisition(requisitionId) {
  console.log('🚀 发起提交领用单请求:', requisitionId)

  if (!requisitionId) {
    return Promise.reject(new Error('领用单ID不能为空'))
  }

  return put(`/item/requisition/${requisitionId}/submit`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 提交领用单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '提交领用单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 提交领用单失败:', error)
    throw error
  })
}

/**
 * 确认领用单
 * 对接后端 PUT /item/requisition/{requisitionId}/confirm 接口
 * @param {string} requisitionId - 领用单ID
 * @param {string} remark - 确认备注（可选）
 * @returns {Promise}
 */
export function confirmRequisition(requisitionId, remark = '') {
  console.log('🚀 发起确认领用单请求:', requisitionId, remark)

  if (!requisitionId) {
    return Promise.reject(new Error('领用单ID不能为空'))
  }

  const params = remark ? { remark } : {}

  return put(`/item/requisition/${requisitionId}/confirm`, params, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 确认领用单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '确认领用单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 确认领用单失败:', error)
    throw error
  })
}

/**
 * 审核领用单
 * 对接后端 PUT /item/requisition/{requisitionId}/audit 接口
 * @param {string} requisitionId - 领用单ID
 * @param {number} status - 审核结果：4-通过, 5-退回
 * @param {string} remark - 审核备注（可选）
 * @returns {Promise}
 */
export function auditRequisition(requisitionId, status, remark = '') {
  console.log('🚀 发起审核领用单请求:', requisitionId, status, remark)

  if (!requisitionId) {
    return Promise.reject(new Error('领用单ID不能为空'))
  }

  if (![4, 5].includes(status)) {
    return Promise.reject(new Error('审核状态无效'))
  }

  const params = { status }
  if (remark) {
    params.remark = remark
  }

  return put(`/item/requisition/${requisitionId}/audit`, params, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 审核领用单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '审核领用单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 审核领用单失败:', error)
    throw error
  })
}

/**
 * 删除领用单
 * 对接后端 DELETE /item/requisition/{requisitionIds} 接口
 * @param {string|array} requisitionIds - 领用单ID，多个用逗号分隔或数组
 * @returns {Promise}
 */
export function deleteRequisition(requisitionIds) {
  console.log('🚀 发起删除领用单请求:', requisitionIds)

  if (!requisitionIds) {
    return Promise.reject(new Error('领用单ID不能为空'))
  }

  const ids = Array.isArray(requisitionIds) ? requisitionIds.join(',') : requisitionIds

  return del(`/item/requisition/${ids}`, {}, {
    showLoading: true,
    showError: false
  }).then(response => {
    console.log('📥 删除领用单原始响应:', response)

    if (response && typeof response === 'object') {
      if (response.code === 200) {
        return response
      } else {
        throw new Error(response.msg || '删除领用单失败')
      }
    } else {
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 删除领用单失败:', error)
    throw error
  })
}
