/**
 * 设备监控系统 - 通用组件样式
 * 基于 Vant Weapp 的组件样式增强
 * 创建时间: 2025-01-07
 */

/* ==================== 页面容器样式 ==================== */

/* 页面主容器 */
.page-container {
  min-height: 100vh;
  background-color: var(--van-background-color);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 内容容器 */
.content-container {
  padding: var(--spacing-lg);
}

.content-container--no-padding {
  padding: 0;
}

.content-container--horizontal {
  padding: 0 var(--spacing-lg);
}

.content-container--vertical {
  padding: var(--spacing-lg) 0;
}

/* ==================== 导航栏样式 ==================== */

/* 自定义导航栏 */
.custom-navbar {
  background-color: var(--van-background-2);
  border-bottom: 1px solid var(--van-border-color);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.custom-navbar__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.custom-navbar__back {
  padding: var(--spacing-sm);
  margin-left: calc(var(--spacing-sm) * -1);
}

.custom-navbar__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* ==================== 卡片样式 ==================== */

/* 基础卡片 */
.card {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}

.card--bordered {
  border: 1px solid var(--van-border-color);
  box-shadow: none;
}

.card--hoverable {
  transition: all var(--animation-duration-fast);
}

.card--hoverable:active {
  transform: translateY(2rpx);
  box-shadow: var(--shadow-base);
}

/* 卡片头部 */
.card__header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--van-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.card__extra {
  color: var(--van-text-color-2);
  font-size: var(--font-size-sm);
}

/* 卡片内容 */
.card__body {
  padding: var(--spacing-lg);
}

.card__body--no-padding {
  padding: 0;
}

/* 卡片底部 */
.card__footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--van-border-color);
  background-color: var(--van-gray-1);
}

/* ==================== 列表样式 ==================== */

/* 列表容器 */
.list-container {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

/* 列表项 */
.list-item {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--van-border-color);
  display: flex;
  align-items: center;
  transition: background-color var(--animation-duration-fast);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: var(--van-gray-1);
}

.list-item--clickable {
  cursor: pointer;
}

/* 列表项图标 */
.list-item__icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: var(--spacing-md);
  color: var(--van-text-color-2);
}

/* 列表项内容 */
.list-item__content {
  flex: 1;
  min-width: 0;
}

.list-item__title {
  font-size: var(--font-size-md);
  color: var(--van-text-color);
  margin-bottom: var(--spacing-xs);
}

.list-item__desc {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
  line-height: var(--line-height-sm);
}

/* 列表项右侧 */
.list-item__extra {
  margin-left: var(--spacing-md);
  color: var(--van-text-color-2);
  font-size: var(--font-size-sm);
}

.list-item__arrow {
  margin-left: var(--spacing-sm);
  color: var(--van-text-color-3);
}

/* ==================== 表单样式 ==================== */

/* 表单容器 */
.form-container {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-light);
}

/* 表单项 */
.form-item {
  margin-bottom: var(--spacing-lg);
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item__label {
  font-size: var(--font-size-md);
  color: var(--van-text-color);
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
}

.form-item__label--required::before {
  content: '*';
  color: var(--van-danger-color);
  margin-right: var(--spacing-xs);
}

.form-item__help {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
  margin-top: var(--spacing-xs);
  line-height: var(--line-height-xs);
}

.form-item__error {
  font-size: var(--font-size-xs);
  color: var(--van-danger-color);
  margin-top: var(--spacing-xs);
}

/* ==================== 标签样式 ==================== */

/* 状态标签 */
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  line-height: 1;
}

.status-tag--success {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--van-success-color);
}

.status-tag--warning {
  background-color: rgba(255, 151, 106, 0.1);
  color: var(--van-warning-color);
}

.status-tag--danger {
  background-color: rgba(238, 10, 36, 0.1);
  color: var(--van-danger-color);
}

.status-tag--info {
  background-color: rgba(25, 137, 250, 0.1);
  color: var(--van-info-color);
}

.status-tag--default {
  background-color: var(--van-gray-2);
  color: var(--van-text-color-2);
}

/* ==================== 加载状态样式 ==================== */

/* 加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  color: var(--van-text-color-2);
}

.loading-container__text {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-sm);
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  color: var(--van-text-color-3);
}

.empty-container__icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-container__text {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-sm);
}

.empty-container__desc {
  font-size: var(--font-size-sm);
  text-align: center;
  line-height: var(--line-height-sm);
}

/* ==================== 分割线样式 ==================== */

/* 分割线 */
.divider {
  height: 1px;
  background-color: var(--van-border-color);
  margin: var(--spacing-lg) 0;
}

.divider--dashed {
  border-top: 1px dashed var(--van-border-color);
  background: none;
  height: 0;
}

.divider--thick {
  height: 16rpx;
  background-color: var(--van-background-color);
  margin: 0;
}

/* 带文字的分割线 */
.divider-with-text {
  display: flex;
  align-items: center;
  margin: var(--spacing-lg) 0;
  color: var(--van-text-color-2);
  font-size: var(--font-size-sm);
}

.divider-with-text::before,
.divider-with-text::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--van-border-color);
}

.divider-with-text::before {
  margin-right: var(--spacing-md);
}

.divider-with-text::after {
  margin-left: var(--spacing-md);
}

/* ==================== 徽章样式 ==================== */

/* 数字徽章 */
.badge {
  position: relative;
  display: inline-block;
}

.badge__dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background-color: var(--van-danger-color);
  border-radius: 50%;
  transform: translate(50%, -50%);
}

.badge__count {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 32rpx;
  height: 32rpx;
  background-color: var(--van-danger-color);
  color: white;
  font-size: var(--font-size-xs);
  line-height: 32rpx;
  text-align: center;
  border-radius: 16rpx;
  padding: 0 var(--spacing-xs);
  transform: translate(50%, -50%);
}

/* ==================== 响应式工具类 ==================== */

/* 小屏幕隐藏 */
@media (max-width: 768rpx) {
  .hidden-sm {
    display: none !important;
  }
}

/* 大屏幕隐藏 */
@media (min-width: 769rpx) {
  .hidden-lg {
    display: none !important;
  }
}
