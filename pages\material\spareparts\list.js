// pages/material/spareparts/list.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { getItemList } from '../../../api/material.js'

Page({
  data: {
    // 搜索条件
    searchKeyword: '',
    
    // 库存列表
    inventoryList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 10,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 物品类型：2-备品备件
    itemType: 2
  },

  onLoad() {
    console.log('📋 备品备件清单页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('📋 初始化备品备件清单页面')
    this.loadInventoryList()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    this.setData({
      'pageInfo.current': 1,
      inventoryList: [],
      finished: false
    })
    await this.loadInventoryList()
  },

  /**
   * 下拉刷新
   */
  async onRefresh() {
    this.setData({ refreshing: true })
    await this.refreshData()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 上拉加载更多
   */
  async onLoadMore() {
    if (this.data.finished || this.data.loading) {
      return
    }

    const { current, pages } = this.data.pageInfo
    if (current >= pages) {
      this.setData({ finished: true })
      return
    }

    this.setData({
      'pageInfo.current': current + 1
    })
    await this.loadInventoryList(false)
  },

  /**
   * 加载库存列表
   */
  async loadInventoryList(reset = true) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size,
        itemType: this.data.itemType // 固定为备品备件类型：2
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.trim()
        // 根据关键词特征判断搜索类型
        if (/^[A-Z0-9]+$/i.test(keyword)) {
          // 如果是字母数字组合，优先按编码搜索
          params.itemCode = keyword
        } else {
          // 否则按名称搜索
          params.itemName = keyword
        }
      }

      console.log('📋 备品备件查询参数:', params)

      // 调用后端接口获取库存数据
      const response = await getItemList(params)
      
      if (response && response.code === 200 && response.data) {
        const { records, total, size, current, pages } = response.data
        
        // 处理数据格式，确保与界面显示一致
        const processedRecords = records.map(item => ({
          itemId: item.itemId,
          itemName: item.itemName,
          itemCode: item.itemCode,
          specModel: item.specModel || '',
          unit: item.unit || '个',
          totalQuantity: item.totalQuantity || 0,
          safetyStock: item.safetyStock || 0,
          stockStatus: item.minStockStatus || 1,
          stockStatusName: item.stockStatusName || '正常',
          warehouseCount: item.warehouseCount || 1,
          imageUrl: item.imageUrl || '/images/default-avatar.png',
          itemType: item.itemType,
          itemTypeName: item.itemTypeName || '备品备件',
          // 备品备件特有字段
          partCategory: item.partCategory,
          partCategoryName: item.partCategoryName || '常用',
          applicableDevice: item.applicableDevice || '通用',
          replacementCycle: item.replacementCycle
        }))

        const newList = reset ? processedRecords : [...this.data.inventoryList, ...processedRecords]
        
        this.setData({
          inventoryList: newList,
          pageInfo: {
            current: current,
            size: size,
            total: total,
            pages: pages
          },
          finished: current >= pages
        })

        console.log('✅ 备品备件清单加载成功:', {
          total: total,
          current: current,
          pages: pages,
          records: processedRecords.length
        })

      } else {
        throw new Error(response?.msg || '获取数据失败')
      }

    } catch (error) {
      console.error('❌ 加载备品备件清单失败:', error)
      
      // 如果是第一次加载失败，显示模拟数据作为备用
      if (reset && this.data.inventoryList.length === 0) {
        console.log('🔄 网络异常，使用模拟数据作为备用')
        const mockData = this.getMockSparepartsData()
        const newList = mockData.records
        
        this.setData({
          inventoryList: newList,
          pageInfo: {
            current: mockData.current,
            size: mockData.size,
            total: mockData.total,
            pages: mockData.pages
          },
          finished: mockData.current >= mockData.pages
        })
        
        // 显示友好的错误提示
        if (error.message && error.message.includes('网络')) {
          Toast.fail('网络连接异常，显示离线数据')
        } else if (error.message && error.message.includes('超时')) {
          Toast.fail('请求超时，显示离线数据')
        } else {
          Toast.fail('服务异常，显示离线数据')
        }
      } else {
        // 非首次加载失败，显示具体错误信息
        const errorMsg = error.message || '加载失败'
        if (errorMsg.includes('401') || errorMsg.includes('登录')) {
          Toast.fail('登录已过期，请重新登录')
        } else if (errorMsg.includes('403') || errorMsg.includes('权限')) {
          Toast.fail('没有访问权限')
        } else if (errorMsg.includes('网络')) {
          Toast.fail('网络连接异常')
        } else {
          Toast.fail('加载失败，请稍后重试')
        }
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 获取模拟备品备件数据
   */
  getMockSparepartsData() {
    const mockItems = [
      {
        itemId: 'SPARE001',
        itemName: '螺丝刀',
        itemCode: 'SD001',
        specModel: '十字头 6mm',
        unit: '把',
        totalQuantity: 100.00,
        safetyStock: 10.00,
        stockStatus: 1,
        stockStatusName: '正常',
        warehouseCount: 2,
        imageUrl: '/images/default-avatar.png',
        itemType: 2,
        itemTypeName: '备品备件',
        partCategory: 1,
        partCategoryName: '关键',
        applicableDevice: '设备A型号',
        replacementCycle: 30
      },
      {
        itemId: 'SPARE002',
        itemName: '扳手',
        itemCode: 'BS001',
        specModel: '活动扳手 200mm',
        unit: '把',
        totalQuantity: 5.00,
        safetyStock: 10.00,
        stockStatus: 2,
        stockStatusName: '不足',
        warehouseCount: 1,
        imageUrl: '/images/default-avatar.png',
        itemType: 2,
        itemTypeName: '备品备件',
        partCategory: 2,
        partCategoryName: '常用',
        applicableDevice: '通用',
        replacementCycle: null
      },
      {
        itemId: 'SPARE003',
        itemName: '密封圈',
        itemCode: 'MFQ001',
        specModel: 'O型圈 φ50',
        unit: '个',
        totalQuantity: 8.00,
        safetyStock: 15.00,
        stockStatus: 2,
        stockStatusName: '不足',
        warehouseCount: 1,
        imageUrl: '/images/default-avatar.png',
        itemType: 2,
        itemTypeName: '备品备件',
        partCategory: 1,
        partCategoryName: '关键',
        applicableDevice: '压缩机',
        replacementCycle: 90
      }
    ]

    return {
      records: mockItems,
      total: mockItems.length,
      size: 10,
      current: 1,
      pages: 1
    }
  },

  /**
   * 搜索输入 - 使用节流优化
   */
  onSearchInput(event) {
    const keyword = event.detail
    this.setData({
      searchKeyword: keyword
    })
    
    // 清除之前的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    
    // 设置新的定时器，300ms后执行搜索
    this.searchTimer = setTimeout(() => {
      this.performSearch(keyword)
    }, 300)
  },

  /**
   * 搜索按钮点击
   */
  onSearch() {
    this.performSearch(this.data.searchKeyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行备品备件搜索，关键词:', keyword)
    
    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      inventoryList: [],
      finished: false
    })
    
    // 重新加载数据
    this.loadInventoryList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 库存项点击
   */
  onInventoryItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击备品备件项:', item)
    
    // 显示加载提示
    Toast.loading('加载中...')
    
    // 跳转到库存详情页面
    wx.navigateTo({
      url: `/pages/material/inventory/detail?itemId=${item.itemId}`,
      success: () => {
        Toast.clear()
      },
      fail: () => {
        Toast.clear()
        Toast.fail('库存详情功能开发中')
      }
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
