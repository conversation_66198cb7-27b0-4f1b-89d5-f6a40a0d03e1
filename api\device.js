/**
 * 设备监控系统 - 设备相关API
 * 对接后端DeviceController接口
 * 创建时间: 2025-01-07
 */

import { post, get } from '../utils/request.js'

/**
 * 获取设备列表
 * 对接后端 POST /device/list 接口
 * @param {object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.id - 设备ID（可选）
 * @param {string} params.deviceName - 设备名称（可选）
 * @param {string} params.deviceModel - 设备型号（可选）
 * @param {string} params.deviceStatus - 设备状态（可选）
 * @param {string} params.location - 设备位置（可选）
 * @returns {Promise}
 */
export function getDeviceList(params = {}) {
  console.log('🚀 发起获取设备列表请求:', params)

  const defaultParams = {
    pageNum: 1,
    pageSize: 10,
    ...params
  }

  return post('/device/list', defaultParams, {
    showLoading: true,
    showError: false  // 先关闭自动错误提示，我们手动处理
  }).then(response => {
    console.log('📥 设备列表原始响应:', response)

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 情况1: RuoYi标准格式 {code: 200, msg: "操作成功", data: {total: xx, rows: []}}
      if (response.code === 200 && response.data) {
        console.log('✅ 标准RuoYi格式响应')
        const data = response.data
        // 转换MyBatis Plus格式到标准格式
        if (data.records) {
          return {
            total: data.total,
            rows: data.records
          }
        }
        return data
      }
      // 情况2: MyBatis Plus分页格式 {total: xx, records: [], current: xx, pages: xx}
      else if (response.hasOwnProperty('total') && response.hasOwnProperty('records')) {
        console.log('✅ MyBatis Plus分页格式响应')
        return {
          total: response.total,
          rows: response.records
        }
      }
      // 情况3: 直接分页格式 {total: xx, rows: []}
      else if (response.hasOwnProperty('total') && response.hasOwnProperty('rows')) {
        console.log('✅ 直接分页格式响应')
        return response
      }
      // 情况4: 其他错误格式
      else {
        console.error('❌ 未知的响应格式:', response)
        console.error('❌ 响应字段:', Object.keys(response))
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      console.error('❌ 无效的响应数据:', response)
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取设备列表失败:', error)
    throw error
  })
}

/**
 * 获取设备详细信息
 * 对接后端 GET /device/list/detail 接口
 * @param {number} deviceId - 设备ID
 * @returns {Promise}
 */
export function getDeviceDetail(deviceId) {
  console.log('🚀 发起获取设备详情请求:', { deviceId })

  return get('/device/list/detail', { deviceId }, {
    showLoading: true,
    showError: false  // 先关闭自动错误提示，由页面处理
  }).then(response => {
    console.log('📥 设备详情原始响应:', response)

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 情况1: RuoYi标准格式 {code: 200, msg: "操作成功", data: {...}}
      if (response.code === 200 && response.data) {
        console.log('✅ 标准RuoYi格式响应')
        return response
      }
      // 情况2: 直接返回设备数据
      else if (response.id || response.deviceName) {
        console.log('✅ 直接设备数据格式响应')
        return {
          code: 200,
          msg: '操作成功',
          data: response
        }
      }
      // 情况3: 其他错误格式
      else {
        console.error('❌ 未知的响应格式:', response)
        throw new Error(response.msg || '响应格式错误')
      }
    } else {
      console.error('❌ 无效的响应数据:', response)
      throw new Error('无效的响应数据')
    }
  }).catch(error => {
    console.error('❌ 获取设备详情失败:', error)
    throw error
  })
}

/**
 * 添加新设备
 * 对接后端 POST /device/add 接口
 * @param {object} deviceInfo - 设备信息
 * @returns {Promise}
 */
export function addDevice(deviceInfo) {
  console.log('🚀 发起添加设备请求:', deviceInfo)

  return post('/device/add', deviceInfo, {
    showLoading: true,
    showError: true
  }).catch(error => {
    console.error('❌ 添加设备失败:', error)
    throw error
  })
}

/**
 * 刷新设备缓存
 * 对接后端 GET /device/refresh 接口
 * @returns {Promise}
 */
export function refreshDeviceCache() {
  console.log('🚀 发起刷新设备缓存请求')

  return get('/device/refresh', {}, {
    showLoading: true,
    showError: true
  }).catch(error => {
    console.error('❌ 刷新设备缓存失败:', error)
    throw error
  })
}

/**
 * 获取设备一般参数
 * 对接后端 GET /device/list/detail/normal 接口
 * @param {number} deviceId - 设备ID
 * @returns {Promise}
 */
export function getDeviceNormalParams(deviceId) {
  console.log('🚀 发起获取设备一般参数请求:', { deviceId })

  return get('/device/list/detail/normal', { deviceId }, {
    showLoading: false,  // 关闭加载提示
    showError: false     // 关闭错误提示，由页面处理
  }).catch(error => {
    console.error('❌ 获取设备一般参数失败:', error)
    throw error
  })
}

/**
 * 获取设备告警信息
 * 对接后端 GET /device/alert/info 接口
 * @param {number} deviceId - 设备ID
 * @returns {Promise}
 */
export function getDeviceAlertInfo(deviceId) {
  console.log('🚀 发起获取设备告警信息请求:', { deviceId })

  return get('/device/alert/info', { deviceId }, {
    showLoading: false,  // 关闭加载提示
    showError: false     // 关闭错误提示，由页面处理
  }).catch(error => {
    console.error('❌ 获取设备告警信息失败:', error)
    throw error
  })
}

/**
 * 获取设备控制参数
 * 对接后端 GET /device/control/params 接口
 * @param {number} deviceId - 设备ID
 * @returns {Promise}
 */
export function getDeviceControlParams(deviceId) {
  console.log('🚀 发起获取设备控制参数请求:', { deviceId })

  return get('/device/control/params', { deviceId }, {
    showLoading: false,  // 关闭加载提示
    showError: false     // 关闭错误提示，由页面处理
  }).catch(error => {
    console.error('❌ 获取设备控制参数失败:', error)
    throw error
  })
}

/**
 * 写入单个设备参数
 * 对接后端 POST /device/param/write 接口
 * @param {number} deviceId - 设备ID
 * @param {string} paramName - 参数名称
 * @param {string} paramValue - 参数值
 * @returns {Promise}
 */
export function writeDeviceParam(deviceId, paramName, paramValue) {
  console.log('🚀 发起写入设备参数请求:', { deviceId, paramName, paramValue })

  const params = {
    deviceId: deviceId,
    paramName: paramName,
    paramValue: paramValue
  }

  return post('/device/param/write', params, {
    showLoading: true,  // 写入操作显示加载提示
    showError: true     // 写入操作显示错误提示
  }).catch(error => {
    console.error('❌ 写入设备参数失败:', error)
    throw error
  })
}
