<!--pages/material/stocktaking/list.wxml-->
<view class="page-container">
  
  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索盘点单号或盘点名称"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onSearch">搜索</view>
    </van-search>
    
    <view class="search-actions">
      <van-button 
        type="primary" 
        size="small" 
        icon="plus" 
        bind:click="onAddStocktaking"
      >新增</van-button>
    </view>
  </view>
  
  <!-- 列表区域 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      refresher-triggered="{{ refreshing }}"
      bind:refresherrefresh="onRefresh"
      bind:scrolltolower="onLoadMore"
    >
      <!-- 盘点计划列表 -->
      <view class="stocktaking-list">
        <block wx:if="{{ stocktakingList.length > 0 }}">
          <van-cell
            wx:for="{{ stocktakingList }}"
            wx:key="stocktakingId"
            bind:click="onStocktakingItemClick"
            data-item="{{ item }}"
            custom-class="stocktaking-item"
            use-slot
          >
            <view class="item-content">
              <view class="item-main">
                <view class="item-header">
                  <view class="item-title">
                    <text class="stocktaking-name">{{ item.stocktakingName }}</text>
                    <van-tag 
                      type="primary" 
                      size="medium"
                      color="{{ statusMap[item.status].color }}"
                    >{{ statusMap[item.status].label }}</van-tag>
                  </view>
                  <view class="item-code">{{ item.stocktakingCode }}</view>
                </view>
                
                <view class="item-info">
                  <view class="info-row">
                    <text class="info-label">盘点类型：</text>
                    <text class="info-value">{{ typeMap[item.stocktakingType] }}</text>
                  </view>
                  <view class="info-row" wx:if="{{ item.warehouseName }}">
                    <text class="info-label">仓库：</text>
                    <text class="info-value">{{ item.warehouseName }}</text>
                  </view>
                  <view class="info-row" wx:if="{{ item.planStartTime }}">
                    <text class="info-label">计划时间：</text>
                    <text class="info-value">{{ item.planStartTime }} ~ {{ item.planEndTime }}</text>
                  </view>
                  <view class="info-row" wx:if="{{ item.remark }}">
                    <text class="info-label">备注：</text>
                    <text class="info-value">{{ item.remark }}</text>
                  </view>
                </view>
                
                <view class="item-footer">
                  <view class="footer-info">
                    <text class="create-time">{{ item.createTime }}</text>
                    <text class="creator">创建人：{{ item.creatorName || '未知' }}</text>
                  </view>
                  <view class="footer-actions">
                    <!-- 生成明细按钮：只有草稿状态可以生成明细 -->
                    <van-button 
                      wx:if="{{ item.status === 0 }}"
                      type="info" 
                      size="mini" 
                      bind:click="onGenerateDetails"
                      data-item="{{ item }}"
                      catch:tap="true"
                    >生成明细</van-button>
                    
                    <!-- 开始盘点按钮：只有草稿状态可以开始盘点 -->
                    <van-button 
                      wx:if="{{ item.status === 0 }}"
                      type="primary" 
                      size="mini" 
                      bind:click="onStartStocktaking"
                      data-item="{{ item }}"
                      catch:tap="true"
                    >开始盘点</van-button>
                  </view>
                </view>
              </view>
            </view>
          </van-cell>
        </block>
        
        <!-- 空状态 -->
        <van-empty 
          wx:else 
          description="暂无盘点计划数据" 
          image="search"
        />
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{ stocktakingList.length > 0 }}">
        <van-loading 
          wx:if="{{ loading }}" 
          type="spinner" 
          size="20"
        >加载中...</van-loading>
        <text wx:elif="{{ finished }}" class="load-finished">没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
  
  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
