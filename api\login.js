/**
 * 设备监控系统 - 登录相关API
 * 对接后端登录接口，包括微信登录、账号绑定等
 * 创建时间: 2025-01-07
 */

import { post, get } from '../utils/request.js'

/**
 * 微信小程序登录
 * 对接后端 /wx/login 接口
 * @param {string} code - 微信登录凭证
 * @param {object} userInfo - 用户信息（可选）
 * @returns {Promise}
 */
export function wxLogin(code, userInfo = {}) {
  console.log('🚀 发起微信登录请求:', {
    code: code,
    userInfo: userInfo
  })

  return post('/wx/login', {
    code: code,
    userInfo: {
      nickName: userInfo.nickName || '',
      avatarUrl: userInfo.avatarUrl || ''
    }
  }, {
    showLoading: true,
    showError: false  // 先关闭自动错误提示，由调用方处理
  }).catch(error => {
    console.error('❌ 微信登录请求失败:', error)
    throw error
  })
}

/**
 * 系统账号登录
 * 对接后端 /login 接口
 * @param {string} username - 系统用户名
 * @param {string} password - 系统密码
 * @returns {Promise}
 */
export function systemLogin(username, password) {
  return post('/login', {
    username: username,
    password: password
  }, {
    showLoading: true,
    showError: false
  })
}

/**
 * 绑定微信账户
 * 对接后端 /wx/bind 接口
 * @param {string} code - 微信登录凭证
 * @returns {Promise}
 */
export function bindWxAccount(code) {
  return post('/wx/bind', {
    code: code
  }, {
    showLoading: true,
    showError: true
  })
}

/**
 * 获取用户信息
 * 对接后端 /getInfo 接口
 * @returns {Promise}
 */
export function getUserInfo() {
  return get('/getInfo', {}, {
    showLoading: false,
    showError: true
  })
}

/**
 * 用户登出
 * 对接后端 /logout 接口
 * @returns {Promise}
 */
export function logout() {
  return post('/logout', {}, {
    showLoading: true,
    showError: false
  })
}

/**
 * 获取路由信息
 * 对接后端 /getRouters 接口
 * @returns {Promise}
 */
export function getRouters() {
  return get('/getRouters', {}, {
    showLoading: false,
    showError: true
  })
}

/**
 * 微信小程序授权登录流程
 * 封装完整的微信登录流程
 * @param {object} userInfo - 用户信息（可选）
 * @returns {Promise}
 */
export function wxLoginFlow(userInfo = {}) {
  return new Promise((resolve, reject) => {
    // 1. 调用微信登录API获取code
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 2. 使用code调用后端接口
          wxLogin(loginRes.code, userInfo)
            .then(response => {
              resolve(response)
            })
            .catch(error => {
              reject(error)
            })
        } else {
          reject(new Error('微信登录失败：' + loginRes.errMsg))
        }
      },
      fail: (error) => {
        reject(new Error('微信登录失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 获取微信用户信息
 * 使用新的getUserProfile API
 * @returns {Promise}
 */
export function getWxUserProfile() {
  return new Promise((resolve, reject) => {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        resolve(res.userInfo)
      },
      fail: (error) => {
        reject(new Error('获取用户信息失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 检查微信授权状态
 * @returns {Promise}
 */
export function checkWxAuthStatus() {
  return new Promise((resolve, reject) => {
    wx.getSetting({
      success: (res) => {
        resolve({
          hasUserInfo: res.authSetting['scope.userInfo'],
          hasUserLocation: res.authSetting['scope.userLocation']
        })
      },
      fail: (error) => {
        reject(new Error('检查授权状态失败：' + error.errMsg))
      }
    })
  })
}

/**
 * 完整的登录流程
 * 包括微信授权、获取用户信息、后端登录
 * @param {boolean} needUserInfo - 是否需要获取用户详细信息
 * @returns {Promise}
 */
export function completeLoginFlow(needUserInfo = false) {
  return new Promise(async (resolve, reject) => {
    try {
      let userInfo = {}
      
      // 如果需要用户信息，先获取
      if (needUserInfo) {
        try {
          userInfo = await getWxUserProfile()
        } catch (error) {
          // 用户拒绝授权，继续登录流程但不传用户信息
          console.warn('用户拒绝授权用户信息:', error.message)
        }
      }
      
      // 执行微信登录流程
      const loginResult = await wxLoginFlow(userInfo)
      resolve(loginResult)
      
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 静默登录
 * 不获取用户信息，仅进行登录验证
 * @returns {Promise}
 */
export function silentLogin() {
  return wxLoginFlow()
}

/**
 * 检查登录状态
 * 验证当前Token是否有效
 * @returns {Promise}
 */
export function checkLoginStatus() {
  return getUserInfo()
}

/**
 * 刷新用户信息
 * 重新获取最新的用户信息
 * @returns {Promise}
 */
export function refreshUserInfo() {
  return getUserInfo()
}
