/* pages/material/stocktaking/add.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 表单区域 */
.form-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 24rpx 32rpx 16rpx;
}

/* 底部按钮 */
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.footer-buttons .van-button {
  flex: 1;
}

/* 选择器样式 */
.picker-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-content {
  flex: 1;
  overflow-y: auto;
  background: #f5f5f5;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .section-title {
    font-size: 30rpx;
    padding: 20rpx 24rpx 12rpx;
  }

  .footer-buttons {
    padding: 20rpx 24rpx;
    gap: 20rpx;
  }
}