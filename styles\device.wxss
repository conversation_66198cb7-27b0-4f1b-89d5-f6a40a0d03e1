/**
 * 设备监控系统 - 设备专用样式
 * 设备状态、数据展示、监控界面专用样式
 * 创建时间: 2025-01-07
 */

/* ==================== 设备状态样式 ==================== */

/* 设备状态指示器 */
.device-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.device-status::before {
  content: '';
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

/* 在线状态 */
.device-status--online {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--device-online);
}

.device-status--online::before {
  background-color: var(--device-online);
}

/* 离线状态 */
.device-status--offline {
  background-color: rgba(200, 201, 204, 0.1);
  color: var(--device-offline);
}

.device-status--offline::before {
  background-color: var(--device-offline);
}

/* 警告状态 */
.device-status--warning {
  background-color: rgba(255, 151, 106, 0.1);
  color: var(--device-warning);
}

.device-status--warning::before {
  background-color: var(--device-warning);
}

/* 故障状态 */
.device-status--error {
  background-color: rgba(238, 10, 36, 0.1);
  color: var(--device-error);
}

.device-status--error::before {
  background-color: var(--device-error);
}

/* 维护状态 */
.device-status--maintenance {
  background-color: rgba(25, 137, 250, 0.1);
  color: var(--device-maintenance);
}

.device-status--maintenance::before {
  background-color: var(--device-maintenance);
}

/* ==================== 数据卡片样式 ==================== */

/* 数据展示卡片 */
.data-card {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
}

.data-card__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.data-card__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.data-card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
  margin-top: var(--spacing-xs);
}

.data-card__content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 数据项 */
.data-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
}

.data-item__label {
  font-size: var(--font-size-md);
  color: var(--van-text-color-2);
}

.data-item__value {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.data-item__unit {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-3);
  margin-left: var(--spacing-xs);
}

/* 数据值颜色变体 */
.data-item__value--primary { color: var(--van-primary-color); }
.data-item__value--success { color: var(--van-success-color); }
.data-item__value--warning { color: var(--van-warning-color); }
.data-item__value--danger { color: var(--van-danger-color); }

/* ==================== 监控图表样式 ==================== */

/* 图表容器 */
.chart-container {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.chart-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.chart-legend {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.chart-legend__item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
}

.chart-legend__dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

/* ==================== 设备列表样式 ==================== */

/* 设备列表项 */
.device-item {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-light);
  transition: all var(--animation-duration-fast);
}

.device-item:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-base);
}

.device-item__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.device-item__name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.device-item__id {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-3);
  margin-top: var(--spacing-xs);
}

.device-item__info {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.device-item__info-item {
  display: flex;
  flex-direction: column;
  min-width: 0;
  flex: 1;
}

.device-item__info-label {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
  margin-bottom: var(--spacing-xs);
}

.device-item__info-value {
  font-size: var(--font-size-md);
  color: var(--van-text-color);
  font-weight: 500;
}

/* ==================== 报警样式 ==================== */

/* 报警横幅 */
.alert-banner {
  background: linear-gradient(135deg, #ff976a 0%, #ff6b35 100%);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

.alert-banner__icon {
  margin-right: var(--spacing-md);
  font-size: var(--font-size-xl);
}

.alert-banner__content {
  flex: 1;
}

.alert-banner__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.alert-banner__desc {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

/* 报警列表项 */
.alert-item {
  background-color: var(--van-background-2);
  border-left: 6rpx solid var(--van-danger-color);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
  box-shadow: var(--shadow-light);
}

.alert-item--warning {
  border-left-color: var(--van-warning-color);
}

.alert-item--info {
  border-left-color: var(--van-info-color);
}

.alert-item__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.alert-item__level {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.alert-item__level--high {
  background-color: rgba(238, 10, 36, 0.1);
  color: var(--van-danger-color);
}

.alert-item__level--medium {
  background-color: rgba(255, 151, 106, 0.1);
  color: var(--van-warning-color);
}

.alert-item__level--low {
  background-color: rgba(25, 137, 250, 0.1);
  color: var(--van-info-color);
}

.alert-item__time {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
}

.alert-item__message {
  font-size: var(--font-size-md);
  color: var(--van-text-color);
  line-height: var(--line-height-md);
}

/* ==================== 操作按钮样式 ==================== */

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--van-background-2);
  border-top: 1px solid var(--van-border-color);
}

.action-button {
  flex: 1;
  height: 88rpx;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-md);
  font-weight: 600;
}

/* 快速操作按钮 */
.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg);
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  transition: all var(--animation-duration-fast);
}

.quick-action:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-base);
}

.quick-action__icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: var(--spacing-sm);
  color: var(--van-primary-color);
}

.quick-action__text {
  font-size: var(--font-size-sm);
  color: var(--van-text-color);
  text-align: center;
}
