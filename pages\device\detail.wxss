/* pages/device/detail.wxss */

/* ==================== 页面容器 ==================== */
.device-detail-page {
  min-height: 100vh;
  background-color: var(--van-background-color);
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl) 0;
  min-height: 60vh;
}

/* ==================== 错误状态 ==================== */
.error-container {
  padding: var(--spacing-xxl) 0;
  min-height: 60vh;
}

/* ==================== 详情内容 ==================== */
.detail-content {
  padding: var(--spacing-md);
}

/* ==================== 信息组 ==================== */
.info-group {
  margin-bottom: var(--spacing-lg) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  overflow: hidden !important;
}

/* ==================== 图片画廊 ==================== */
.image-gallery {
  padding: var(--spacing-md);
}

.image-item {
  border-radius: var(--border-radius-md) !important;
  overflow: hidden !important;
}

/* ==================== 操作区域 ==================== */
.action-section {
  padding: var(--spacing-lg) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.action-btn {
  margin: 0 !important;
}

/* ==================== 离线提示区域 ==================== */
.offline-tip-section {
  padding: var(--spacing-lg) 0;
}

.offline-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--van-border-color);
}

.tip-text {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
}

/* ==================== 设备状态样式 ==================== */
.device-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.device-status::before {
  content: '';
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

/* 在线状态 */
.device-status--online {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--device-online);
}

.device-status--online::before {
  background-color: var(--device-online);
}

/* 离线状态 */
.device-status--offline {
  background-color: rgba(200, 201, 204, 0.1);
  color: var(--device-offline);
}

.device-status--offline::before {
  background-color: var(--device-offline);
}

/* ==================== 信息项样式 ==================== */
.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--van-border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--font-size-md);
  color: var(--van-text-color);
  font-weight: 500;
  min-width: 160rpx;
}

.info-value {
  font-size: var(--font-size-md);
  color: var(--van-text-color-2);
  flex: 1;
  text-align: right;
}

.info-desc {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-3);
  margin-top: var(--spacing-xs);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .detail-content {
    padding: var(--spacing-sm);
  }
  
  .action-section {
    padding: var(--spacing-md) 0;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  
  .info-label {
    min-width: auto;
  }
  
  .info-value {
    text-align: left;
  }
}

/* ==================== 动画效果 ==================== */
.info-group {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
