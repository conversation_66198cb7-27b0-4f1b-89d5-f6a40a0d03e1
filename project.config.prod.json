{"compileType": "miniprogram", "libVersion": "release", "packOptions": {"ignore": ["config/config.dev.js", "config/env.js", "doc/**/*"], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "uglifyFileName": true, "uploadWithSourceMap": false, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false, "useCompilerPlugins": false}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wxPRODUCTION_APP_ID", "simulatorPluginLibVersion": {}, "description": "生产环境配置"}