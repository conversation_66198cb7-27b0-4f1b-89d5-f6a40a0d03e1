/* pages/monitor/device.wxss */

/* ==================== 页面容器 ==================== */
.device-monitor-page {
  min-height: 100vh;
  background-color: var(--van-background-color);
}

/* ==================== 搜索区域 ==================== */
.search-section {
  background-color: var(--van-background-2);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-light);
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.refresh-btn {
  padding: var(--spacing-sm);
  color: var(--van-primary-color);
}

.debug-btn {
  padding: var(--spacing-sm);
  color: var(--van-info-color);
}

/* ==================== 设备列表区域 ==================== */
.device-list-section {
  padding: var(--spacing-md);
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl) 0;
}

.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg) 0;
}

.no-more-container {
  padding: var(--spacing-lg) 0;
}

/* ==================== 空状态 ==================== */
.empty-container {
  padding: var(--spacing-xxl) 0;
}

/* ==================== 设备卡片 ==================== */
.device-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.device-card-wrapper {
  cursor: pointer;
  transition: all var(--animation-duration-fast);
}

.device-card-wrapper:active {
  transform: scale(0.98);
}

.device-card {
  background-color: var(--van-background-2) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  transition: all var(--animation-duration-fast) !important;
}

.device-card-wrapper:active .device-card {
  box-shadow: var(--shadow-base) !important;
}

/* ==================== 设备状态标签 ==================== */
.status-tag {
  margin-left: var(--spacing-xs) !important;
}

/* ==================== 设备信息 ==================== */
.device-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--van-border-color);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.info-label {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
  min-width: 120rpx;
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--van-text-color);
  flex: 1;
}

/* ==================== 设备状态样式 ==================== */
.device-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.device-status::before {
  content: '';
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: var(--spacing-xs);
}

/* 在线状态 */
.device-status--online {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--device-online);
}

.device-status--online::before {
  background-color: var(--device-online);
}

/* 离线状态 */
.device-status--offline {
  background-color: rgba(200, 201, 204, 0.1);
  color: var(--device-offline);
}

.device-status--offline::before {
  background-color: var(--device-offline);
}

/* 告警状态 */
.device-status--warning {
  background-color: rgba(255, 151, 106, 0.1);
  color: var(--device-warning);
}

.device-status--warning::before {
  background-color: var(--device-warning);
}

/* 故障状态 */
.device-status--error {
  background-color: rgba(238, 10, 36, 0.1);
  color: var(--device-error);
}

.device-status--error::before {
  background-color: var(--device-error);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .device-info {
    flex-direction: column;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    min-width: auto;
  }
}