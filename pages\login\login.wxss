/* pages/login/login.wxss */

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  display: flex;
  flex-direction: column;
}

/* ==================== 顶部品牌区域 ==================== */
.login-header {
  padding: 120rpx var(--spacing-xl) 80rpx;
  text-align: center;
  color: white;
}

.brand-logo {
  margin-bottom: var(--spacing-lg);
}

.brand-title {
  font-size: var(--font-size-xxl);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  letter-spacing: 2rpx;
}

.brand-subtitle {
  font-size: var(--font-size-md);
  opacity: 0.9;
  font-weight: 300;
}

/* ==================== 登录内容区域 ==================== */
.login-content {
  flex: 1;
  background-color: var(--van-background-color);
  border-radius: 40rpx 40rpx 0 0;
  padding: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.login-section {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.title-text {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--van-text-color);
  margin-bottom: var(--spacing-sm);
}

.title-desc {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
}

/* ==================== 微信登录区域 ==================== */
.wx-login-area {
  text-align: center;
}

.wx-login-btn {
  width: 100% !important;
  height: 96rpx !important;
  margin-bottom: var(--spacing-lg);
}

.wx-login-btn .van-button__text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.login-tips {
  margin-top: var(--spacing-md);
}

.tip-text {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
}

/* ==================== 系统登录表单 ==================== */
.login-form {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.login-actions {
  padding: var(--spacing-lg);
}

.login-btn {
  width: 100% !important;
  height: 96rpx !important;
}

/* ==================== 分割线 ==================== */
.section-divider {
  display: flex;
  align-items: center;
  margin: var(--spacing-xl) 0;
  position: relative;
}

.section-divider::before {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--van-border-color);
}

.section-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--van-border-color);
}

.divider-text {
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: var(--van-text-color-3);
  background-color: var(--van-background-color);
}

/* ==================== 登录状态提示 ==================== */
.login-status {
  margin-top: var(--spacing-lg);
}

.status-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  gap: var(--spacing-sm);
}

.status-message.success {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--van-success-color);
}

.status-message.error {
  background-color: rgba(238, 10, 36, 0.1);
  color: var(--van-danger-color);
}

.status-message.warning {
  background-color: rgba(255, 151, 106, 0.1);
  color: var(--van-warning-color);
}

.status-message.info {
  background-color: rgba(25, 137, 250, 0.1);
  color: var(--van-info-color);
}

.status-text {
  font-size: var(--font-size-sm);
}

/* ==================== 底部信息 ==================== */
.login-footer {
  padding: var(--spacing-xl);
  text-align: center;
}

.footer-links {
  margin-bottom: var(--spacing-md);
}

.link-item {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
  text-decoration: underline;
}

.link-divider {
  margin: 0 var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
}

.footer-copyright {
  margin-top: var(--spacing-sm);
}

.copyright-text {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
}

/* ==================== 响应式适配 ==================== */
@media (max-height: 1200rpx) {
  .login-header {
    padding: 80rpx var(--spacing-xl) 60rpx;
  }
  
  .brand-title {
    font-size: var(--font-size-xl);
  }
  
  .login-content {
    padding: var(--spacing-lg);
  }
}

/* ==================== 动画效果 ==================== */
.login-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ==================== 自定义组件样式 ==================== */
.van-field__left-icon {
  color: var(--van-primary-color) !important;
}

.van-field__control {
  font-size: var(--font-size-md) !important;
}

.van-cell-group {
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
}
