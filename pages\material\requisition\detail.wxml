<!--pages/material/requisition/detail.wxml-->
<view class="page-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{ loading }}" type="spinner" size="24">加载中...</van-loading>

  <!-- 领用单详情 -->
  <view wx:else class="detail-container">
    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-header">
        <view class="section-title">基本信息</view>
        <van-tag
          type="primary"
          size="medium"
          color="{{ statusMap[requisitionDetail.status].color }}"
        >{{ statusMap[requisitionDetail.status].label }}</van-tag>
      </view>

      <van-cell-group>
        <van-cell title="领用单号" value="{{ requisitionDetail.requisitionId }}" />
        <van-cell title="业务日期" value="{{ requisitionDetail.businessDate }}" />
        <van-cell title="申请人" value="{{ requisitionDetail.applicantName || '未知' }}" />
        <van-cell title="申请部门" value="{{ requisitionDetail.deptName || '未知' }}" />
        <van-cell
          title="领用用途"
          value="{{ requisitionDetail.requisitionPurpose || '无' }}"
          label="{{ requisitionDetail.requisitionPurpose }}"
          wx:if="{{ requisitionDetail.requisitionPurpose }}"
        />
      </van-cell-group>
    </view>

    <!-- 领用明细 -->
    <view class="info-section">
      <view class="section-title">领用明细</view>

      <view class="detail-list">
        <view
          class="detail-item"
          wx:for="{{ requisitionDetail.details }}"
          wx:key="detailId"
        >
          <view class="item-header">
            <view class="item-name">{{ item.itemName }}</view>
            <view class="item-code">{{ item.itemCode }}</view>
          </view>

          <view class="item-content">
            <view class="item-row">
              <text class="label">规格型号：</text>
              <text class="value">{{ item.specModel || '无' }}</text>
            </view>
            <view class="item-row">
              <text class="label">仓库：</text>
              <text class="value">{{ item.warehouseName || '未知' }}</text>
            </view>
            <view class="item-row">
              <text class="label">申请数量：</text>
              <text class="value">{{ item.requisitionQuantity }} {{ item.unit }}</text>
            </view>
            <view class="item-row" wx:if="{{ item.approvedQuantity }}">
              <text class="label">批准数量：</text>
              <text class="value">{{ item.approvedQuantity }} {{ item.unit }}</text>
            </view>
            <view class="item-row" wx:if="{{ item.actualQuantity }}">
              <text class="label">实际数量：</text>
              <text class="value">{{ item.actualQuantity }} {{ item.unit }}</text>
            </view>
            <view class="item-row" wx:if="{{ item.shelfLocation }}">
              <text class="label">货架位置：</text>
              <text class="value">{{ item.shelfLocation }}</text>
            </view>
            <view class="item-row" wx:if="{{ item.remark }}">
              <text class="label">备注：</text>
              <text class="value">{{ item.remark }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作记录 -->
    <view class="info-section" wx:if="{{ requisitionDetail.createTime }}">
      <view class="section-title">操作记录</view>

      <van-cell-group>
        <van-cell
          title="创建时间"
          value="{{ requisitionDetail.createTime }}"
          label="制单人：{{ requisitionDetail.creatorName || '未知' }}"
        />
        <van-cell
          wx:if="{{ requisitionDetail.handleTime }}"
          title="确认时间"
          value="{{ requisitionDetail.handleTime }}"
          label="经手人：{{ requisitionDetail.handlerName || '未知' }}"
        />
        <van-cell
          wx:if="{{ requisitionDetail.auditTime }}"
          title="审核时间"
          value="{{ requisitionDetail.auditTime }}"
          label="审核人：{{ requisitionDetail.auditorName || '未知' }}"
        />
      </van-cell-group>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <!-- 编辑按钮：草稿、已退回状态可编辑 -->
      <van-button
        wx:if="{{ requisitionDetail.status === 1 || requisitionDetail.status === 5 }}"
        type="primary"
        size="large"
        bind:click="onEdit"
      >编辑</van-button>

      <!-- 提交按钮：草稿、已退回状态可提交 -->
      <van-button
        wx:if="{{ requisitionDetail.status === 1 || requisitionDetail.status === 5 }}"
        type="info"
        size="large"
        bind:click="onSubmit"
      >提交</van-button>

      <!-- 确认按钮：待确认状态可确认 -->
      <van-button
        wx:if="{{ requisitionDetail.status === 2 }}"
        type="warning"
        size="large"
        bind:click="onConfirm"
      >确认</van-button>

      <!-- 审核按钮：待审核状态可审核 -->
      <van-button
        wx:if="{{ requisitionDetail.status === 3 }}"
        type="primary"
        size="large"
        bind:click="onAudit"
      >审核</van-button>
    </view>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>