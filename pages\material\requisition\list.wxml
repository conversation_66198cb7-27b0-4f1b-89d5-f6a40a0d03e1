<!--pages/material/requisition/list.wxml-->
<view class="page-container">
  
  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索领用单号或领用用途"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onSearch">搜索</view>
    </van-search>
    
    <view class="search-actions">
      <van-button 
        type="primary" 
        size="small" 
        icon="plus" 
        bind:click="onAddRequisition"
      >新增</van-button>
    </view>
  </view>
  
  <!-- 列表区域 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      refresher-triggered="{{ refreshing }}"
      bind:refresherrefresh="onRefresh"
      bind:scrolltolower="onLoadMore"
    >
      <!-- 领用单列表 -->
      <view class="requisition-list">
        <block wx:if="{{ requisitionList.length > 0 }}">
          <van-cell
            wx:for="{{ requisitionList }}"
            wx:key="requisitionId"
            bind:click="onRequisitionItemClick"
            data-item="{{ item }}"
            custom-class="requisition-item"
            use-slot
          >
            <view class="item-content">
              <view class="item-main">
                <view class="item-header">
                  <view class="item-title">
                    <text class="requisition-id">{{ item.requisitionId }}</text>
                    <van-tag 
                      type="primary" 
                      size="medium"
                      color="{{ statusMap[item.status].color }}"
                    >{{ statusMap[item.status].label }}</van-tag>
                  </view>
                  <view class="item-date">{{ item.businessDate }}</view>
                </view>
                
                <view class="item-info">
                  <view class="info-row">
                    <text class="info-label">申请人：</text>
                    <text class="info-value">{{ item.applicantName || '未知' }}</text>
                  </view>
                  <view class="info-row">
                    <text class="info-label">申请部门：</text>
                    <text class="info-value">{{ item.deptName || '未知' }}</text>
                  </view>
                  <view class="info-row" wx:if="{{ item.requisitionPurpose }}">
                    <text class="info-label">领用用途：</text>
                    <text class="info-value">{{ item.requisitionPurpose }}</text>
                  </view>
                </view>
                
                <view class="item-footer">
                  <view class="footer-info">
                    <text class="create-time">{{ item.createTime }}</text>
                  </view>
                  <view class="footer-actions">
                    <!-- 删除按钮：只有草稿和已退回状态可以删除 -->
                    <van-button 
                      wx:if="{{ item.status === 1 || item.status === 5 }}"
                      type="danger" 
                      size="mini" 
                      icon="delete-o"
                      bind:click="onDeleteRequisition"
                      data-item="{{ item }}"
                      catch:tap="true"
                    >删除</van-button>
                  </view>
                </view>
              </view>
            </view>
          </van-cell>
        </block>
        
        <!-- 空状态 -->
        <van-empty 
          wx:else 
          description="暂无领用单数据" 
          image="search"
        />
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{ requisitionList.length > 0 }}">
        <van-loading 
          wx:if="{{ loading }}" 
          type="spinner" 
          size="20"
        >加载中...</van-loading>
        <text wx:elif="{{ finished }}" class="load-finished">没有更多数据了</text>
      </view>
    </scroll-view>
  </view>
  
  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
