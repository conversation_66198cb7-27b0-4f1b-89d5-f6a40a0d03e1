<!--pages/material/consumables/list.wxml-->
<view class="page-container">
  
  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索消耗品名称或编码"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>

  <!-- 库存列表 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y="true"
      bindscrolltolower="onLoadMore"
      refresher-enabled="true"
      refresher-triggered="{{ refreshing }}"
      bindrefresherrefresh="onRefresh"
    >
      <!-- 使用 van-cell-group 包装列表 -->
      <van-cell-group wx:if="{{ inventoryList.length > 0 }}">
        <van-cell
          wx:for="{{ inventoryList }}"
          wx:key="itemId"
          custom-class="inventory-item"
          bind:click="onInventoryItemClick"
          data-item="{{ item }}"
          is-link
          use-slot
        >
          <!-- 自定义内容 -->
          <view class="item-content">
            <!-- 物品图片 -->
            <view class="item-image">
              <van-image
                src="{{ item.imageUrl }}"
                width="80rpx"
                height="80rpx"
                radius="8rpx"
                error-icon="photo-fail"
              />
            </view>
            
            <!-- 物品信息 -->
            <view class="item-info">
              <view class="item-header">
                <text class="item-name">{{ item.itemName }}</text>
                <view class="item-tags">
                  <van-tag type="primary" size="mini">{{ item.itemTypeName }}</van-tag>
                  <van-tag
                    type="{{ item.stockStatus === 1 ? 'success' : 'danger' }}"
                    size="mini"
                  >
                    {{ item.stockStatusName }}
                  </van-tag>
                </view>
              </view>
              
              <view class="item-details">
                <text class="item-code">编码: {{ item.itemCode }}</text>
                <text class="item-spec">规格: {{ item.specModel }}</text>
              </view>
              
              <view class="item-stock">
                <text class="stock-quantity">库存: {{ item.totalQuantity }} {{ item.unit }}</text>
                <text class="safety-stock">安全库存: {{ item.safetyStock }} {{ item.unit }}</text>
              </view>
              
              <view class="item-warehouse">
                <text class="warehouse-count">分布仓库: {{ item.warehouseCount }} 个</text>
              </view>
            </view>
          </view>
        </van-cell>
      </van-cell-group>

      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{ loading }}">
        <van-loading type="spinner" size="24px">加载中...</van-loading>
      </view>

      <!-- 加载完成提示 -->
      <view class="finished-container" wx:if="{{ finished && inventoryList.length > 0 }}">
        <text class="finished-text">没有更多了</text>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <van-empty
      wx:if="{{ !loading && inventoryList.length === 0 }}"
      description="暂无消耗品数据"
      image="search"
    />
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
