/**
 * 设备监控系统 - 全局状态管理
 * 简单的状态管理器，用于管理用户信息和登录状态
 * 创建时间: 2025-01-07
 */

import { getUserInfo, isLoggedIn, getAuthStatus } from './auth.js'
import { refreshUserInfo } from '../api/login.js'

// 全局状态
let globalState = {
  // 用户信息
  user: null,
  // 登录状态
  isLoggedIn: false,
  // 权限信息
  roles: [],
  permissions: [],
  // 应用状态
  loading: false,
  // 设备信息
  deviceCount: {
    total: 0,
    online: 0,
    offline: 0,
    warning: 0,
    error: 0
  }
}

// 状态监听器
let listeners = []

/**
 * 获取全局状态
 */
function getState() {
  return { ...globalState }
}

/**
 * 更新状态
 */
function setState(newState) {
  const oldState = { ...globalState }
  globalState = { ...globalState, ...newState }
  
  // 通知所有监听器
  listeners.forEach(listener => {
    try {
      listener(globalState, oldState)
    } catch (error) {
      console.error('状态监听器执行错误:', error)
    }
  })
}

/**
 * 添加状态监听器
 */
function subscribe(listener) {
  if (typeof listener !== 'function') {
    throw new Error('监听器必须是函数')
  }
  
  listeners.push(listener)
  
  // 返回取消订阅的函数
  return function unsubscribe() {
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }
}

/**
 * 初始化状态
 */
function initState() {
  const authStatus = getAuthStatus()
  const userInfo = getUserInfo()
  
  setState({
    user: userInfo,
    isLoggedIn: authStatus.isLoggedIn,
    roles: userInfo?.roles || [],
    permissions: userInfo?.permissions || []
  })
}

/**
 * 设置用户信息
 */
function setUser(userInfo) {
  setState({
    user: userInfo,
    isLoggedIn: true,
    roles: userInfo?.roles || [],
    permissions: userInfo?.permissions || []
  })
}

/**
 * 清除用户信息
 */
function clearUser() {
  setState({
    user: null,
    isLoggedIn: false,
    roles: [],
    permissions: []
  })
}

/**
 * 设置加载状态
 */
function setLoading(loading) {
  setState({ loading })
}

/**
 * 更新设备统计信息
 */
function updateDeviceCount(deviceCount) {
  setState({ deviceCount })
}

/**
 * 刷新用户信息
 */
async function refreshUser() {
  if (!isLoggedIn()) {
    clearUser()
    return false
  }
  
  try {
    setLoading(true)
    const response = await refreshUserInfo()
    
    if (response && response.user) {
      setUser(response.user)
      return true
    } else {
      clearUser()
      return false
    }
  } catch (error) {
    console.error('刷新用户信息失败:', error)
    clearUser()
    return false
  } finally {
    setLoading(false)
  }
}

/**
 * 检查权限
 */
function hasPermission(permission) {
  const { permissions } = globalState
  if (!permissions || permissions.length === 0) {
    return false
  }
  
  // 超级管理员权限
  if (permissions.includes('*:*:*')) {
    return true
  }
  
  return permissions.includes(permission)
}

/**
 * 检查角色
 */
function hasRole(role) {
  const { roles } = globalState
  if (!roles || roles.length === 0) {
    return false
  }
  
  return roles.includes(role)
}

/**
 * 获取用户信息
 */
function getUser() {
  return globalState.user
}

/**
 * 获取登录状态
 */
function getLoginStatus() {
  return globalState.isLoggedIn
}

/**
 * 获取权限列表
 */
function getPermissions() {
  return globalState.permissions
}

/**
 * 获取角色列表
 */
function getRoles() {
  return globalState.roles
}

/**
 * 获取设备统计信息
 */
function getDeviceCount() {
  return globalState.deviceCount
}

/**
 * 页面状态管理Mixin
 * 在页面中使用，自动同步状态
 */
function createPageMixin(mapState = () => ({})) {
  return {
    data: {
      // 全局状态映射到页面data
      ...mapState(globalState)
    },
    
    onLoad() {
      // 订阅状态变化
      this._unsubscribe = subscribe((newState, oldState) => {
        const mappedState = mapState(newState)
        if (Object.keys(mappedState).length > 0) {
          this.setData(mappedState)
        }
      })
      
      // 初始化状态
      const mappedState = mapState(globalState)
      if (Object.keys(mappedState).length > 0) {
        this.setData(mappedState)
      }
    },
    
    onUnload() {
      // 取消订阅
      if (this._unsubscribe) {
        this._unsubscribe()
      }
    }
  }
}

/**
 * 组件状态管理Mixin
 * 在组件中使用，自动同步状态
 */
function createComponentMixin(mapState = () => ({})) {
  return {
    data: {
      // 全局状态映射到组件data
      ...mapState(globalState)
    },
    
    attached() {
      // 订阅状态变化
      this._unsubscribe = subscribe((newState, oldState) => {
        const mappedState = mapState(newState)
        if (Object.keys(mappedState).length > 0) {
          this.setData(mappedState)
        }
      })
      
      // 初始化状态
      const mappedState = mapState(globalState)
      if (Object.keys(mappedState).length > 0) {
        this.setData(mappedState)
      }
    },
    
    detached() {
      // 取消订阅
      if (this._unsubscribe) {
        this._unsubscribe()
      }
    }
  }
}

// 导出方法
export {
  // 基础状态管理
  getState,
  setState,
  subscribe,
  initState,
  
  // 用户状态管理
  setUser,
  clearUser,
  refreshUser,
  getUser,
  getLoginStatus,
  
  // 权限管理
  hasPermission,
  hasRole,
  getPermissions,
  getRoles,
  
  // 应用状态
  setLoading,
  updateDeviceCount,
  getDeviceCount,
  
  // Mixin
  createPageMixin,
  createComponentMixin
}
