<!--pages/material/requisition/add.wxml-->
<view class="page-container">
  <!-- 基本信息表单 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>
    
    <van-cell-group>
      <!-- 业务日期 -->
      <van-cell 
        title="业务日期" 
        value="{{ formData.businessDate }}" 
        is-link
        required
        bind:click="onShowDatePicker"
      />
      
      <!-- 申请人 -->
      <van-cell 
        title="申请人" 
        value="{{ applicantName || '请选择申请人' }}" 
        is-link
        required
        bind:click="onShowApplicantPicker"
      />
      
      <!-- 申请部门 -->
      <van-cell 
        title="申请部门" 
        value="{{ deptName || '请选择申请部门' }}" 
        is-link
        required
        bind:click="onShowDeptPicker"
      />
      
      <!-- 领用用途 -->
      <van-field
        label="领用用途"
        value="{{ formData.requisitionPurpose }}"
        placeholder="请输入领用用途"
        type="textarea"
        autosize
        required
        data-field="requisitionPurpose"
        bind:change="onFieldChange"
      />
    </van-cell-group>
  </view>
  
  <!-- 领用明细 -->
  <view class="form-section">
    <view class="section-header">
      <view class="section-title">领用明细</view>
      <van-button 
        type="primary" 
        size="small" 
        icon="plus" 
        bind:click="onAddDetail"
      >添加物品</van-button>
    </view>
    
    <!-- 明细列表 -->
    <view class="detail-list">
      <block wx:if="{{ detailList.length > 0 }}">
        <view 
          class="detail-item" 
          wx:for="{{ detailList }}" 
          wx:key="index"
        >
          <view class="detail-header">
            <view class="detail-title">
              <text class="detail-name">{{ item.itemName }}</text>
              <text class="detail-code">({{ item.itemCode }})</text>
            </view>
            <view class="detail-actions">
              <van-icon 
                name="delete-o" 
                size="20" 
                color="#ee0a24" 
                data-index="{{ index }}"
                bind:click="onDeleteDetail"
              />
            </view>
          </view>
          
          <view class="detail-content">
            <view class="detail-info">
              <text class="detail-spec">{{ item.specModel }}</text>
              <text class="detail-unit">单位: {{ item.unit }}</text>
            </view>
            
            <view class="detail-warehouse">
              <text>仓库: {{ item.warehouseName }}</text>
            </view>
            
            <view class="detail-fields">
              <!-- 申请数量 -->
              <van-field
                label="申请数量"
                value="{{ item.requisitionQuantity }}"
                type="digit"
                required
                input-align="right"
                data-field="requisitionQuantity"
                data-index="{{ index }}"
                bind:change="onDetailFieldChange"
              />
              
              <!-- 货架位置 -->
              <van-field
                label="货架位置"
                value="{{ item.shelfLocation }}"
                placeholder="请输入货架位置"
                input-align="right"
                data-field="shelfLocation"
                data-index="{{ index }}"
                bind:change="onDetailFieldChange"
              />
              
              <!-- 备注 -->
              <van-field
                label="备注"
                value="{{ item.remark }}"
                placeholder="请输入备注"
                input-align="right"
                data-field="remark"
                data-index="{{ index }}"
                bind:change="onDetailFieldChange"
              />
            </view>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-detail" wx:else>
        <text>请添加领用物品</text>
      </view>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="footer-buttons">
    <van-button 
      type="default" 
      size="large" 
      bind:click="onCancel"
    >取消</van-button>
    <van-button 
      type="primary" 
      size="large" 
      loading="{{ submitting }}"
      bind:click="onSave"
    >保存</van-button>
  </view>
  
  <!-- 日期选择器 -->
  <van-popup
    show="{{ showDatePicker }}"
    position="bottom"
    bind:close="onDateCancel"
  >
    <van-datetime-picker
      type="date"
      value="{{ minDate }}"
      min-date="{{ minDate }}"
      max-date="{{ maxDate }}"
      bind:confirm="onDateConfirm"
      bind:cancel="onDateCancel"
    />
  </van-popup>
  
  <!-- 申请人选择弹窗 -->
  <van-popup
    show="{{ showApplicantPicker }}"
    position="bottom"
    bind:close="onCloseApplicantPicker"
  >
    <view class="picker-container">
      <view class="picker-header">
        <view class="picker-title">选择申请人</view>
        <van-icon name="cross" bind:click="onCloseApplicantPicker" />
      </view>
      
      <view class="picker-content">
        <van-cell-group>
          <van-cell
            wx:for="{{ applicantList }}"
            wx:key="userId"
            title="{{ item.userName }}"
            label="{{ item.deptName }}"
            bind:click="onApplicantSelect"
            data-applicant="{{ item }}"
            is-link
          />
        </van-cell-group>
      </view>
    </view>
  </van-popup>
  
  <!-- 部门选择弹窗 -->
  <van-popup
    show="{{ showDeptPicker }}"
    position="bottom"
    bind:close="onCloseDeptPicker"
  >
    <view class="picker-container">
      <view class="picker-header">
        <view class="picker-title">选择申请部门</view>
        <van-icon name="cross" bind:click="onCloseDeptPicker" />
      </view>
      
      <view class="picker-content">
        <van-cell-group>
          <van-cell
            wx:for="{{ deptList }}"
            wx:key="deptId"
            title="{{ item.deptName }}"
            bind:click="onDeptSelect"
            data-dept="{{ item }}"
            is-link
          />
        </van-cell-group>
      </view>
    </view>
  </van-popup>
  
  <!-- 物品选择弹窗 -->
  <van-popup
    show="{{ showItemPicker }}"
    position="bottom"
    custom-style="height: 80%;"
    bind:close="onCloseItemPicker"
  >
    <view class="picker-container">
      <view class="picker-header">
        <view class="picker-title">选择物品</view>
        <van-icon name="cross" bind:click="onCloseItemPicker" />
      </view>
      
      <view class="picker-search">
        <van-search
          value="{{ itemSearchKeyword }}"
          placeholder="搜索物品名称或编码"
          bind:change="onItemSearchInput"
          bind:search="onItemSearch"
          use-action-slot
        >
          <view slot="action" bind:tap="onItemSearch">搜索</view>
        </van-search>
      </view>
      
      <view class="picker-content">
        <van-cell-group wx:if="{{ itemList.length > 0 }}">
          <van-cell
            wx:for="{{ itemList }}"
            wx:key="itemId"
            bind:click="onItemSelect"
            data-item="{{ item }}"
            use-slot
          >
            <view class="item-cell">
              <view class="item-info">
                <view class="item-name">{{ item.itemName }}</view>
                <view class="item-code">{{ item.itemCode }}</view>
              </view>
              <view class="item-spec">
                <text>{{ item.specModel }}</text>
                <text class="item-unit">{{ item.unit }}</text>
              </view>
            </view>
          </van-cell>
        </van-cell-group>
        
        <van-empty 
          wx:else 
          description="暂无物品数据" 
          image="search"
        />
      </view>
    </view>
  </van-popup>
  
  <!-- 仓库选择弹窗 -->
  <van-popup
    show="{{ showWarehousePicker }}"
    position="bottom"
    bind:close="onCloseWarehousePicker"
  >
    <view class="picker-container">
      <view class="picker-header">
        <view class="picker-title">选择仓库</view>
        <van-icon name="cross" bind:click="onCloseWarehousePicker" />
      </view>
      
      <view class="picker-content">
        <van-cell-group>
          <van-cell
            wx:for="{{ warehouseList }}"
            wx:key="warehouseId"
            title="{{ item.warehouseName }}"
            bind:click="onWarehouseSelect"
            data-warehouse="{{ item }}"
            is-link
          />
        </van-cell-group>
      </view>
    </view>
  </van-popup>
  
  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
