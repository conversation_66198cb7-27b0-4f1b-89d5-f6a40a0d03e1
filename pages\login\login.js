// pages/login/login.js

import { completeLoginFlow, systemLogin, bindWxAccount } from '../../api/login.js'
import { setToken, setUserInfo, setOpenId, isLoggedIn, clearAll } from '../../utils/auth.js'
import Dialog from '@vant/weapp/dialog/dialog'
import networkDiagnostic from '../../utils/network-diagnostic.js'
import { getCurrentConfig } from '../../config/env.js'

Page({
  data: {
    // 系统登录表单
    username: '',
    password: '',
    loginLoading: false,
    canLogin: false,

    // 微信登录状态
    wxLoginLoading: false,
    showWxLogin: false, // 是否显示微信登录选项

    // 状态提示
    statusMessage: '',
    statusType: '', // success, error, warning, info
    statusIcon: ''
  },

  onLoad(options) {
    console.log('登录页面加载')

    // 检查是否已经登录
    if (isLoggedIn()) {
      this.redirectToMain()
      return
    }

    // 清除可能存在的过期信息
    clearAll()

    // 检查是否有已绑定的微信账户（通过本地存储检查）
    this.checkWxBindStatus()

    // 显示欢迎信息
    this.showStatus('请使用系统账号登录', 'info', 'info')
  },

  onShow() {
    // 页面显示时检查登录状态
    if (isLoggedIn()) {
      this.redirectToMain()
    }
  },

  /**
   * 检查微信绑定状态
   */
  checkWxBindStatus() {
    // 检查本地是否有微信绑定记录
    try {
      const wxBound = wx.getStorageSync('wx_account_bound')
      this.setData({
        showWxLogin: !!wxBound
      })
    } catch (e) {
      console.log('检查微信绑定状态失败:', e)
    }
  },

  /**
   * 系统账号登录
   */
  async handleSystemLogin() {
    if (this.data.loginLoading) return

    const { username, password } = this.data

    if (!username.trim()) {
      this.showStatus('请输入用户名', 'error', 'cross')
      return
    }

    if (!password.trim()) {
      this.showStatus('请输入密码', 'error', 'cross')
      return
    }

    this.setData({
      loginLoading: true,
      statusMessage: ''
    })

    try {
      console.log('🔐 开始系统账号登录')

      // 调用系统登录接口
      const result = await systemLogin(username.trim(), password.trim())

      console.log('✅ 系统登录成功:', result)

      if (result.token) {
        // 登录成功，保存Token和用户信息
        setToken(result.token)

        if (result.user) {
          setUserInfo(result.user)
        }

        this.showStatus('登录成功', 'success', 'success')

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          this.redirectToMain()
        }, 1500)

        // 登录成功后询问是否绑定微信
        setTimeout(() => {
          this.askForWxBinding()
        }, 2000)

      } else {
        throw new Error('登录响应数据异常')
      }

    } catch (error) {
      console.error('❌ 系统登录失败:', error)

      let errorMessage = '登录失败'

      if (error.message.includes('用户名或密码错误')) {
        errorMessage = '用户名或密码错误'
      } else if (error.message.includes('账户被锁定')) {
        errorMessage = '账户被锁定，请联系管理员'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message) {
        errorMessage = error.message
      }

      this.showStatus(errorMessage, 'error', 'cross')
    } finally {
      this.setData({ loginLoading: false })
    }
  },

  /**
   * 微信快速登录（仅已绑定用户）
   */
  async handleWxLogin() {
    if (this.data.wxLoginLoading) return

    this.setData({
      wxLoginLoading: true,
      statusMessage: ''
    })

    try {
      console.log('🔐 开始微信快速登录')

      // 先检查网络状态
      const networkType = await this.checkNetworkStatus()
      console.log('📶 网络状态:', networkType)

      if (networkType === 'none') {
        throw new Error('网络连接失败，请检查网络设置')
      }

      // 执行微信登录流程
      const result = await completeLoginFlow(true)

      console.log('✅ 微信登录成功:', result)

      if (result.token) {
        // 登录成功，保存Token和用户信息
        setToken(result.token)

        if (result.user) {
          setUserInfo(result.user)
        }

        this.showStatus('登录成功', 'success', 'success')

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          this.redirectToMain()
        }, 1500)

      } else {
        throw new Error('微信账户未绑定或绑定信息已失效，请使用系统账号登录')
      }

    } catch (error) {
      console.error('❌ 微信登录失败:', error)

      let errorMessage = '微信登录失败'

      if (error.message.includes('未绑定')) {
        errorMessage = '微信账户未绑定，请使用系统账号登录'
        // 隐藏微信登录选项
        this.setData({ showWxLogin: false })
        wx.removeStorageSync('wx_account_bound')
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络设置'
      } else if (error.message) {
        errorMessage = error.message
      }

      this.showStatus(errorMessage, 'error', 'cross')
    } finally {
      this.setData({ wxLoginLoading: false })
    }
  },

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve(res.networkType)
        },
        fail: () => {
          resolve('unknown')
        }
      })
    })
  },



  /**
   * 询问是否绑定微信
   */
  askForWxBinding() {
    Dialog.confirm({
      title: '绑定微信账户',
      message: '是否要绑定当前微信账户？绑定后可以使用微信快速登录。',
      confirmButtonText: '立即绑定',
      cancelButtonText: '暂不绑定'
    }).then(() => {
      this.bindWxAccount()
    }).catch(() => {
      console.log('用户选择暂不绑定微信')
    })
  },

  /**
   * 绑定微信账户
   */
  async bindWxAccount() {
    try {
      wx.showLoading({ title: '绑定中...' })

      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (!loginRes.code) {
        throw new Error('获取微信授权失败')
      }

      // 调用绑定接口
      const result = await bindWxAccount(loginRes.code)

      wx.hideLoading()

      if (result.code === 200) {
        // 绑定成功，保存绑定状态
        wx.setStorageSync('wx_account_bound', true)
        this.setData({ showWxLogin: true })

        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        })
      } else {
        throw new Error(result.msg || '绑定失败')
      }

    } catch (error) {
      wx.hideLoading()
      console.error('微信绑定失败:', error)

      wx.showModal({
        title: '绑定失败',
        content: error.message || '微信账户绑定失败，请稍后重试',
        showCancel: false
      })
    }
  },

  /**
   * 用户名输入变化
   */
  onUsernameChange(event) {
    this.setData({
      username: event.detail,
      statusMessage: ''
    })
    this.checkCanLogin()
  },

  /**
   * 密码输入变化
   */
  onPasswordChange(event) {
    this.setData({
      password: event.detail,
      statusMessage: ''
    })
    this.checkCanLogin()
  },

  /**
   * 检查是否可以登录
   */
  checkCanLogin() {
    const canLogin = this.data.username.trim().length > 0 &&
                    this.data.password.trim().length > 0
    this.setData({ canLogin })
  },



  /**
   * 显示状态信息
   */
  showStatus(message, type = 'info', icon = 'info') {
    this.setData({
      statusMessage: message,
      statusType: type,
      statusIcon: icon
    })
    
    // 3秒后自动清除状态信息
    setTimeout(() => {
      this.setData({ statusMessage: '' })
    }, 3000)
  },

  /**
   * 跳转到主页面
   */
  redirectToMain() {
    wx.reLaunch({
      url: '/pages/index/index'
    })
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy() {
    Dialog.alert({
      title: '隐私政策',
      message: '我们重视您的隐私保护，详细的隐私政策请访问官网查看。',
    })
  },

  /**
   * 显示用户协议
   */
  showUserAgreement() {
    Dialog.alert({
      title: '用户协议',
      message: '使用本系统即表示您同意我们的用户协议，详细条款请访问官网查看。',
    })
  },

  /**
   * 显示网络诊断
   */
  async showNetworkDiagnostic() {
    const config = getCurrentConfig()

    Dialog.confirm({
      title: '网络诊断',
      message: `当前环境: ${config.ENV_NAME}\nAPI地址: ${config.API_BASE_URL}\n\n是否开始网络诊断？`,
    }).then(async () => {
      await networkDiagnostic.showDiagnosticReport()
    }).catch(() => {
      // 用户取消
    })
  },

  /**
   * 快速连接测试
   */
  async testQuickConnection() {
    const result = await networkDiagnostic.quickConnectionTest()
    return result
  },

  /**
   * 页面卸载
   */
  onUnload() {
    // 清理定时器等资源
  }
})
