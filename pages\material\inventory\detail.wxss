/* pages/material/inventory/detail.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.detail-content {
  height: 100vh;
  padding: 16rpx;
}

/* 区域容器 */
.section-container {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 头部信息 */
.section-header {
  padding: 32rpx 24rpx;
}

.header-content {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.item-image {
  flex-shrink: 0;
}

.header-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.item-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.item-code-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.item-code {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}

.item-tags {
  display: flex;
  gap: 12rpx;
  margin-top: 8rpx;
}

/* 区域标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 24rpx 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  gap: 24rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部间距 */
.bottom-spacing {
  height: 32rpx;
}

/* 自定义组件样式覆盖 */
.van-cell {
  padding: 24rpx !important;
}

.van-cell__title {
  font-size: 28rpx !important;
  color: #333 !important;
}

.van-cell__value {
  font-size: 28rpx !important;
  color: #666 !important;
}

.van-cell__label {
  font-size: 24rpx !important;
  color: #999 !important;
  margin-top: 4rpx !important;
}

/* 折叠面板样式 */
.van-collapse-item__title {
  font-size: 32rpx !important;
  font-weight: 600 !important;
  color: #333 !important;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .header-content {
    gap: 20rpx;
  }

  .item-name {
    font-size: 32rpx;
  }

  .section-title {
    font-size: 30rpx;
    padding: 20rpx 20rpx 12rpx;
  }
}
