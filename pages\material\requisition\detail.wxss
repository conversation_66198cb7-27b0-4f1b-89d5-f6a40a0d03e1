/* pages/material/requisition/detail.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.van-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  color: #1989fa;
}

/* 详情容器 */
.detail-container {
  padding: 16rpx;
}

/* 信息区域 */
.info-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 24rpx 32rpx 16rpx;
}

.section-header .section-title {
  padding: 0;
  margin: 0;
}

/* 明细列表 */
.detail-list {
  padding: 0 16rpx 16rpx;
}

.detail-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #e8e8e8;
}

.item-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.item-code {
  font-size: 24rpx;
  color: #666;
}

.item-content {
  /* 明细内容样式 */
}

.item-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}

.item-row:last-child {
  margin-bottom: 0;
}

.item-row .label {
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.item-row .value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 操作区域 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.action-section .van-button {
  flex: 1;
}

/* 状态标签样式调整 */
.van-tag {
  border-radius: 8rpx !important;
  font-size: 20rpx !important;
  padding: 4rpx 8rpx !important;
}

/* 单元格样式调整 */
.van-cell {
  padding: 24rpx 32rpx !important;
}

.van-cell__title {
  font-size: 28rpx !important;
  color: #333 !important;
}

.van-cell__value {
  font-size: 28rpx !important;
  color: #666 !important;
}

.van-cell__label {
  font-size: 24rpx !important;
  color: #999 !important;
  margin-top: 8rpx !important;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .section-title {
    font-size: 30rpx;
    padding: 20rpx 24rpx 12rpx;
  }

  .detail-item {
    padding: 16rpx;
  }

  .item-name {
    font-size: 28rpx;
  }

  .item-code {
    font-size: 22rpx;
  }

  .item-row {
    font-size: 24rpx;
  }

  .item-row .label {
    min-width: 120rpx;
  }

  .action-section {
    padding: 20rpx 24rpx;
    gap: 20rpx;
  }
}