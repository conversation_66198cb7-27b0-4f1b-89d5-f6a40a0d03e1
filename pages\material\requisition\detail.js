// pages/material/requisition/detail.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { getRequisitionDetail } from '../../../api/material.js'

Page({
  data: {
    // 领用单ID
    requisitionId: '',

    // 领用单详情
    requisitionDetail: null,

    // 加载状态
    loading: true,

    // 状态映射
    statusMap: {
      1: { label: '草稿', color: '#969799' },
      2: { label: '待确认', color: '#ff976a' },
      3: { label: '待审核', color: '#1989fa' },
      4: { label: '已通过', color: '#07c160' },
      5: { label: '已退回', color: '#ee0a24' },
      6: { label: '已完成', color: '#07c160' }
    }
  },

  onLoad(options) {
    console.log('📋 领用单详情页面加载', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取领用单ID
    const { requisitionId } = options
    if (!requisitionId) {
      Toast.fail('领用单ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ requisitionId })

    // 加载领用单详情
    this.loadRequisitionDetail()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.requisitionId) {
      this.loadRequisitionDetail()
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadRequisitionDetail().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载领用单详情
   */
  async loadRequisitionDetail() {
    this.setData({ loading: true })

    try {
      console.log('🔍 加载领用单详情:', this.data.requisitionId)

      const response = await getRequisitionDetail(this.data.requisitionId)

      if (response && response.code === 200 && response.data) {
        console.log('📋 获取到领用单详情:', response.data)

        this.setData({
          requisitionDetail: response.data
        })
      } else {
        throw new Error(response?.msg || '获取领用单详情失败')
      }
    } catch (error) {
      console.error('❌ 加载领用单详情失败:', error)
      Toast.fail('获取详情失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 编辑领用单
   */
  onEdit() {
    const detail = this.data.requisitionDetail

    // 只有草稿和已退回状态可以编辑
    if (![1, 5].includes(detail.status)) {
      Toast.fail('当前状态不允许编辑')
      return
    }

    wx.navigateTo({
      url: `/pages/material/requisition/edit?requisitionId=${this.data.requisitionId}`,
      fail: () => {
        Toast.fail('编辑功能开发中')
      }
    })
  },

  /**
   * 提交领用单
   */
  onSubmit() {
    Toast.fail('提交功能开发中')
  },

  /**
   * 确认领用单
   */
  onConfirm() {
    Toast.fail('确认功能开发中')
  },

  /**
   * 审核领用单
   */
  onAudit() {
    Toast.fail('审核功能开发中')
  }
})