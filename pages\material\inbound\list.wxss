/* pages/material/inbound/list.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 列表区域 */
.list-section {
  flex: 1;
  height: calc(100vh - 120rpx);
}

.scroll-container {
  height: 100%;
  padding: 0 16rpx;
}

/* 重写 van-cell 样式 */
.inbound-item {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.item-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  width: 100%;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  min-width: 0; /* 防止文字溢出 */
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.item-id {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-tags {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-supplier,
.item-date,
.item-creator {
  font-size: 26rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
  margin-top: 8rpx;
}

.item-count {
  font-size: 26rpx;
  color: #666;
}

.item-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff976a; /* 入库单使用橙色 */
}

.item-extra {
  margin-top: 8rpx;
}

.item-remark {
  font-size: 24rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-time {
  margin-top: 8rpx;
}

.create-time {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-container,
.finished-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx;
}

.finished-text {
  font-size: 24rpx;
  color: #999;
}

/* 固定新增按钮 */
.fixed-add-button {
  position: fixed;
  bottom: 32rpx;
  right: 32rpx;
  z-index: 1000;
}

.add-button {
  background-color: #ff976a !important;
  border-color: #ff976a !important;
  box-shadow: 0 4rpx 16rpx rgba(255, 151, 106, 0.3) !important;
}

/* 状态筛选器 */
.status-filter-content {
  max-height: 600rpx;
  overflow-y: auto;
  padding-bottom: 32rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .item-content {
    padding: 20rpx;
    gap: 20rpx;
  }
  
  .item-id {
    font-size: 30rpx;
  }
  
  .item-amount {
    font-size: 26rpx;
  }
}
