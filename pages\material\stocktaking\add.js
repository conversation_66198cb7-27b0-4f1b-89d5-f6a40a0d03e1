// pages/material/stocktaking/add.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { addStocktaking } from '../../../api/material.js'

Page({
  data: {
    // 表单数据
    formData: {
      stocktakingName: '',
      stocktakingType: 1,
      warehouseId: null,
      planStartTime: '',
      planEndTime: '',
      remark: ''
    },

    // 盘点类型选项
    stocktakingTypeOptions: [
      { text: '全盘', value: 1 },
      { text: '抽盘', value: 2 },
      { text: '循环盘点', value: 3 }
    ],

    // 仓库选择相关
    showWarehousePicker: false,
    warehouseList: [
      { warehouseId: 1, warehouseName: '主仓库' },
      { warehouseId: 2, warehouseName: '备用仓库' },
      { warehouseId: 3, warehouseName: '临时仓库' }
    ],

    // 日期时间选择
    showStartTimePicker: false,
    showEndTimePicker: false,
    minDate: new Date().getTime(),
    maxDate: new Date(2030, 11, 31).getTime(),

    // 盘点类型选择
    showTypePicker: false,

    // 页面状态
    submitting: false
  },

  onLoad() {
    console.log('📋 新增盘点计划页面加载')

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 设置默认计划开始时间为今天
    const today = new Date()
    const startTimeStr = this.formatDateTime(today)

    // 设置默认计划结束时间为明天
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
    const endTimeStr = this.formatDateTime(tomorrow)

    this.setData({
      'formData.planStartTime': startTimeStr,
      'formData.planEndTime': endTimeStr
    })

    console.log('✅ 页面数据初始化完成')
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  /**
   * 表单字段变化处理
   */
  onFieldChange(event) {
    const { field } = event.currentTarget.dataset
    const value = event.detail

    this.setData({
      [`formData.${field}`]: value
    })

    console.log(`📝 字段 ${field} 更新为:`, value)
  },

  /**
   * 显示盘点类型选择器
   */
  onShowTypePicker() {
    this.setData({ showTypePicker: true })
  },

  /**
   * 盘点类型选择确认
   */
  onTypeConfirm(event) {
    const { value } = event.detail
    this.setData({
      'formData.stocktakingType': value,
      showTypePicker: false
    })

    // 如果选择全盘，清空仓库选择
    if (value === 1) {
      this.setData({
        'formData.warehouseId': null,
        warehouseName: ''
      })
    }
  },

  /**
   * 盘点类型选择取消
   */
  onTypeCancel() {
    this.setData({ showTypePicker: false })
  },

  /**
   * 显示仓库选择器
   */
  onShowWarehousePicker() {
    // 全盘类型不需要选择仓库
    if (this.data.formData.stocktakingType === 1) {
      Toast.fail('全盘类型不需要选择仓库')
      return
    }

    this.setData({ showWarehousePicker: true })
  },

  /**
   * 仓库选择
   */
  onWarehouseSelect(event) {
    const warehouse = event.currentTarget.dataset.warehouse
    console.log('选择仓库:', warehouse)

    this.setData({
      'formData.warehouseId': warehouse.warehouseId,
      warehouseName: warehouse.warehouseName,
      showWarehousePicker: false
    })
  },

  /**
   * 关闭仓库选择器
   */
  onCloseWarehousePicker() {
    this.setData({ showWarehousePicker: false })
  },

  /**
   * 显示开始时间选择器
   */
  onShowStartTimePicker() {
    this.setData({ showStartTimePicker: true })
  },

  /**
   * 开始时间选择确认
   */
  onStartTimeConfirm(event) {
    const date = new Date(event.detail)
    const timeStr = this.formatDateTime(date)

    this.setData({
      'formData.planStartTime': timeStr,
      showStartTimePicker: false
    })
  },

  /**
   * 开始时间选择取消
   */
  onStartTimeCancel() {
    this.setData({ showStartTimePicker: false })
  },

  /**
   * 显示结束时间选择器
   */
  onShowEndTimePicker() {
    this.setData({ showEndTimePicker: true })
  },

  /**
   * 结束时间选择确认
   */
  onEndTimeConfirm(event) {
    const date = new Date(event.detail)
    const timeStr = this.formatDateTime(date)

    this.setData({
      'formData.planEndTime': timeStr,
      showEndTimePicker: false
    })
  },

  /**
   * 结束时间选择取消
   */
  onEndTimeCancel() {
    this.setData({ showEndTimePicker: false })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData } = this.data

    // 验证盘点名称
    if (!formData.stocktakingName || !formData.stocktakingName.trim()) {
      Toast.fail('请输入盘点名称')
      return false
    }

    // 验证盘点类型
    if (!formData.stocktakingType) {
      Toast.fail('请选择盘点类型')
      return false
    }

    // 验证仓库（抽盘和循环盘点时必填）
    if ([2, 3].includes(formData.stocktakingType) && !formData.warehouseId) {
      Toast.fail('请选择仓库')
      return false
    }

    // 验证计划时间
    if (!formData.planStartTime) {
      Toast.fail('请选择计划开始时间')
      return false
    }

    if (!formData.planEndTime) {
      Toast.fail('请选择计划结束时间')
      return false
    }

    // 验证时间逻辑
    const startTime = new Date(formData.planStartTime)
    const endTime = new Date(formData.planEndTime)

    if (endTime <= startTime) {
      Toast.fail('计划结束时间必须晚于开始时间')
      return false
    }

    return true
  },

  /**
   * 保存盘点计划
   */
  async onSave() {
    if (!this.validateForm()) {
      return
    }

    if (this.data.submitting) {
      return
    }

    this.setData({ submitting: true })

    try {
      // 构建盘点计划数据
      const stocktakingData = {
        stocktakingName: this.data.formData.stocktakingName.trim(),
        stocktakingType: this.data.formData.stocktakingType,
        planStartTime: this.data.formData.planStartTime,
        planEndTime: this.data.formData.planEndTime,
        remark: this.data.formData.remark.trim()
      }

      // 如果不是全盘，添加仓库ID
      if (this.data.formData.stocktakingType !== 1) {
        stocktakingData.warehouseId = this.data.formData.warehouseId
      }

      console.log('💾 保存盘点计划数据:', stocktakingData)

      // 调用API保存盘点计划
      const response = await addStocktaking(stocktakingData)

      if (response && response.code === 200) {
        Toast.success('盘点计划保存成功')

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(response?.msg || '保存失败')
      }
    } catch (error) {
      console.error('❌ 保存盘点计划失败:', error)
      Toast.fail(error.message || '保存失败')
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 取消操作
   */
  onCancel() {
    if (this.data.formData.stocktakingName ||
        this.data.formData.remark) {
      Dialog.confirm({
        title: '取消确认',
        message: '当前有未保存的数据，确定要取消吗？',
        confirmButtonText: '确定取消',
        cancelButtonText: '继续编辑'
      }).then(() => {
        wx.navigateBack()
      }).catch(() => {
        // 继续编辑
      })
    } else {
      wx.navigateBack()
    }
  }
})