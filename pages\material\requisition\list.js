// pages/material/requisition/list.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { getRequisitionList, deleteRequisition } from '../../../api/material.js'

Page({
  data: {
    // 搜索条件
    searchKeyword: '',
    
    // 领用单列表
    requisitionList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 10,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 状态映射
    statusMap: {
      1: { label: '草稿', color: '#969799' },
      2: { label: '待确认', color: '#ff976a' },
      3: { label: '待审核', color: '#1989fa' },
      4: { label: '已通过', color: '#07c160' },
      5: { label: '已退回', color: '#ee0a24' },
      6: { label: '已完成', color: '#07c160' }
    }
  },

  onLoad() {
    console.log('📋 领用单列表页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('📋 初始化领用单列表页面')
    this.loadRequisitionList()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      // 重置分页信息
      this.setData({
        'pageInfo.current': 1,
        requisitionList: [],
        finished: false
      })
      
      // 重新加载数据
      await this.loadRequisitionList(true)
      
      Toast.success('数据已更新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      Toast.fail('刷新失败')
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.refreshData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished) {
      return
    }
    
    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    this.loadRequisitionList()
  },

  /**
   * 加载领用单列表
   */
  async loadRequisitionList(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.trim()
        // 根据关键词特征判断搜索类型
        if (/^REQ[A-Z0-9]+$/i.test(keyword)) {
          // 如果是REQ开头，按领用单ID搜索
          params.requisitionId = keyword
        } else {
          // 否则按领用用途搜索
          params.requisitionPurpose = keyword
        }
      }

      console.log('📋 领用单查询参数:', params)

      // 调用后端接口获取领用单数据
      const response = await getRequisitionList(params)
      
      if (response && response.code === 200 && response.data) {
        const { records, total, size, current, pages } = response.data
        
        console.log('📋 获取到领用单列表:', records)
        
        // 更新列表数据
        let newList = []
        if (isRefresh || this.data.pageInfo.current === 1) {
          // 刷新或首次加载，替换数据
          newList = records || []
        } else {
          // 加载更多，追加数据
          newList = [...this.data.requisitionList, ...(records || [])]
        }
        
        // 更新页面数据
        this.setData({
          requisitionList: newList,
          'pageInfo.total': total || 0,
          'pageInfo.size': size || 10,
          'pageInfo.current': current || 1,
          'pageInfo.pages': pages || 0,
          finished: (current >= pages) || (records && records.length === 0)
        })
        
        console.log('✅ 领用单列表加载完成，共', newList.length, '条记录')
      } else {
        throw new Error(response?.msg || '获取领用单列表失败')
      }
    } catch (error) {
      console.error('❌ 加载领用单列表失败:', error)
      
      // 如果是首次加载失败，显示错误信息
      if (this.data.pageInfo.current === 1) {
        Toast.fail('获取数据失败')
      }
      
      // 恢复页码
      if (this.data.pageInfo.current > 1) {
        this.setData({
          'pageInfo.current': this.data.pageInfo.current - 1
        })
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const value = event.detail
    this.setData({
      searchKeyword: value
    })
    
    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    
    this.searchTimer = setTimeout(() => {
      if (value !== this.data.searchKeyword) return
      this.performSearch(value)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行领用单搜索，关键词:', keyword)
    
    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      requisitionList: [],
      finished: false
    })
    
    // 重新加载数据
    this.loadRequisitionList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 新增领用单
   */
  onAddRequisition() {
    console.log('➕ 新增领用单')
    
    wx.navigateTo({
      url: '/pages/material/requisition/add',
      fail: () => {
        Toast.fail('新增领用单功能开发中')
      }
    })
  },

  /**
   * 领用单项点击
   */
  onRequisitionItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击领用单项:', item)
    
    // 显示加载提示
    Toast.loading('加载中...')
    
    // 跳转到领用单详情页面
    wx.navigateTo({
      url: `/pages/material/requisition/detail?requisitionId=${item.requisitionId}`,
      success: () => {
        Toast.clear()
      },
      fail: () => {
        Toast.clear()
        Toast.fail('领用单详情功能开发中')
      }
    })
  },

  /**
   * 删除领用单
   */
  onDeleteRequisition(event) {
    const item = event.currentTarget.dataset.item
    console.log('删除领用单:', item)
    
    // 只有草稿和已退回状态可以删除
    if (![1, 5].includes(item.status)) {
      Toast.fail('当前状态不允许删除')
      return
    }
    
    Dialog.confirm({
      title: '删除确认',
      message: `确定要删除领用单 ${item.requisitionId} 吗？`,
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        await deleteRequisition(item.requisitionId)
        Toast.success('删除成功')
        
        // 刷新列表
        this.refreshData()
      } catch (error) {
        console.error('删除领用单失败:', error)
        Toast.fail(error.message || '删除失败')
      }
    }).catch(() => {
      // 取消删除
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
