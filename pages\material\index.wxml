<!--pages/material/index.wxml-->
<view class="page-container">

  <!-- 页面标题 -->
  <view class="header-section">
    <view class="header-title">物资管理</view>
    <view class="header-subtitle">请选择要使用的功能</view>
  </view>

  <!-- 菜单列表 -->
  <view class="menu-section">
    <van-cell-group>
      <van-cell
        wx:for="{{menuItems}}"
        wx:key="id"
        title="{{item.title}}"
        label="{{item.subtitle}}"
        is-link
        bind:click="onMenuItemClick"
        data-item="{{item}}"
        custom-class="menu-cell"
        use-slot
      >
        <view slot="icon" class="menu-icon" style="background-color: {{item.color}}20">
          <van-icon name="{{item.icon}}" size="32" color="{{item.color}}" />
        </view>
      </van-cell>
    </van-cell-group>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
