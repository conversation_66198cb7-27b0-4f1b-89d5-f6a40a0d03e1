<!--pages/profile/profile.wxml-->
<view class="page-container">
  <view class="content-container">
    
    <!-- 用户信息卡片 -->
    <view class="card mb-lg">
      <view class="card__header">
        <text class="card__title">用户信息</text>
      </view>
      <view class="card__body">
        <view class="user-info">
          <view class="user-avatar">
            <image src="{{userInfo.avatar || '/images/default-avatar.png'}}" class="avatar-img" />
          </view>
          <view class="user-details">
            <view class="user-name">{{userInfo.nickName || userInfo.userName}}</view>
            <view class="user-role">{{userInfo.roleName || '普通用户'}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 微信绑定设置 -->
    <view class="card mb-lg">
      <view class="card__header">
        <text class="card__title">微信账户</text>
      </view>
      <view class="card__body">
        <van-cell-group>
          <van-cell 
            title="微信绑定状态" 
            value="{{wxBound ? '已绑定' : '未绑定'}}"
            label="{{wxBound ? '可以使用微信快速登录' : '绑定后可使用微信快速登录'}}"
          >
            <van-switch 
              slot="right-icon" 
              checked="{{wxBound}}" 
              bind:change="onWxBindChange"
              disabled="{{wxBindLoading}}"
            />
          </van-cell>
          
          <van-cell 
            wx:if="{{wxBound}}"
            title="解除绑定" 
            is-link
            bind:click="unbindWxAccount"
            value="解除微信绑定"
            label="解除后将无法使用微信登录"
          />
        </van-cell-group>
      </view>
    </view>

    <!-- 账户安全 -->
    <view class="card mb-lg">
      <view class="card__header">
        <text class="card__title">账户安全</text>
      </view>
      <view class="card__body">
        <van-cell-group>
          <van-cell title="修改密码" is-link bind:click="changePassword" />
          <van-cell title="登录日志" is-link bind:click="viewLoginLog" />
          <van-cell title="退出登录" is-link bind:click="logout" />
        </van-cell-group>
      </view>
    </view>

  </view>
  
  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
