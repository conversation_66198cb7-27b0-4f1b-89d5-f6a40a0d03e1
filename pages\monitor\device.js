// pages/monitor/device.js
import { getDeviceList, getDeviceDetail, refreshDeviceCache } from '../../api/device.js'
import { checkPagePermission } from '../../utils/permission.js'
import { showError, showSuccess } from '../../utils/request.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 设备列表数据
    deviceList: [],
    // 分页信息
    pagination: {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      hasMore: true
    },
    // 搜索关键词
    searchKeyword: '',
    // 加载状态
    loading: false,
    // 是否首次加载
    isFirstLoad: true,
    // 空状态
    isEmpty: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备监控页面加载')

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('设备监控页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据（如果不是首次加载）
    if (!this.data.isFirstLoad) {
      this.refreshDeviceList()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新设备列表')
    this.refreshDeviceList().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉加载更多设备')
    this.loadMoreDevices()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '设备监控系统',
      path: '/pages/monitor/device'
    }
  },

  /**
   * 初始化页面数据
   */
  async initPageData() {
    try {
      await this.loadDeviceList()
      this.setData({ isFirstLoad: false })
    } catch (error) {
      console.error('初始化页面数据失败:', error)
      showError('加载设备列表失败')
    }
  },

  /**
   * 加载设备列表
   */
  async loadDeviceList(isLoadMore = false) {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const params = {
        pageNum: isLoadMore ? this.data.pagination.pageNum + 1 : 1,
        pageSize: this.data.pagination.pageSize,
        deviceName: this.data.searchKeyword || undefined
      }

      console.log('📤 加载设备列表参数:', params)
      const result = await getDeviceList(params)

      console.log('📥 设备列表响应:', result)
      console.log('📊 响应数据类型:', typeof result)
      console.log('📋 响应数据键:', result ? Object.keys(result) : 'null')

      if (result && result.rows) {
        const newDeviceList = isLoadMore
          ? [...this.data.deviceList, ...result.rows]
          : result.rows

        this.setData({
          deviceList: newDeviceList,
          pagination: {
            ...this.data.pagination,
            pageNum: params.pageNum,
            total: result.total || 0,
            hasMore: newDeviceList.length < (result.total || 0)
          },
          isEmpty: newDeviceList.length === 0,
          loading: false
        })
      } else {
        throw new Error('响应数据格式错误')
      }
    } catch (error) {
      console.error('❌ 加载设备列表失败:', error)
      console.error('❌ 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })

      this.setData({
        loading: false,
        isEmpty: this.data.deviceList.length === 0
      })

      if (!isLoadMore) {
        // 根据错误类型显示不同的提示
        let errorMsg = '加载设备列表失败'
        if (error.message.includes('网络')) {
          errorMsg = '网络连接失败，请检查网络设置'
        } else if (error.message.includes('超时')) {
          errorMsg = '请求超时，请稍后重试'
        } else if (error.message.includes('操作失败')) {
          errorMsg = '服务器返回错误，请稍后重试'
        }
        showError(errorMsg)
      }
    }
  },

  /**
   * 刷新设备列表
   */
  async refreshDeviceList() {
    this.setData({
      pagination: {
        ...this.data.pagination,
        pageNum: 1,
        hasMore: true
      }
    })
    await this.loadDeviceList()
  },

  /**
   * 加载更多设备
   */
  async loadMoreDevices() {
    if (!this.data.pagination.hasMore || this.data.loading) {
      return
    }
    await this.loadDeviceList(true)
  },

  /**
   * 搜索设备
   */
  onSearchDevice(event) {
    const keyword = event.detail.value || event.detail
    console.log('搜索设备:', keyword)

    this.setData({
      searchKeyword: keyword,
      pagination: {
        ...this.data.pagination,
        pageNum: 1,
        hasMore: true
      }
    })

    this.loadDeviceList()
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({
      searchKeyword: '',
      pagination: {
        ...this.data.pagination,
        pageNum: 1,
        hasMore: true
      }
    })
    this.loadDeviceList()
  },

  /**
   * 点击设备卡片
   */
  onDeviceClick(event) {
    console.log('🔥 设备卡片被点击了!')
    console.log('🔥 事件对象:', event)

    const device = event.currentTarget.dataset.device
    console.log('🔥 获取到的设备数据:', device)

    if (device && device.id) {
      console.log('🔥 准备跳转到设备详情页面, ID:', device.id)
      // 跳转到设备详情页面
      wx.navigateTo({
        url: `/pages/device/detail?id=${device.id}`,
        success: () => {
          console.log('✅ 跳转设备详情页面成功')
        },
        fail: (error) => {
          console.error('❌ 跳转设备详情页面失败:', error)
          showError('跳转失败，请重试')
        }
      })
    } else {
      console.error('❌ 设备数据无效:', device)
      showError('设备信息错误')
    }
  },

  /**
   * 刷新缓存
   */
  async onRefreshCache() {
    try {
      await refreshDeviceCache()
      showSuccess('缓存刷新成功')
      // 重新加载设备列表
      this.refreshDeviceList()
    } catch (error) {
      console.error('刷新缓存失败:', error)
      showError('刷新缓存失败')
    }
  },

  /**
   * 调试API - 显示原始响应数据
   */
  async onDebugApi() {
    try {
      console.log('🔍 开始API调试...')

      // 直接调用API并显示原始响应
      const params = {
        pageNum: 1,
        pageSize: 5
      }

      const result = await getDeviceList(params)

      // 显示调试信息
      const debugInfo = {
        '请求参数': params,
        '响应数据': result,
        '数据类型': typeof result,
        '是否有rows': result && result.hasOwnProperty('rows'),
        '是否有records': result && result.hasOwnProperty('records'),
        '是否有total': result && result.hasOwnProperty('total'),
        'rows长度': result && result.rows ? result.rows.length : 'N/A',
        'records长度': result && result.records ? result.records.length : 'N/A'
      }

      console.log('🔍 API调试信息:', debugInfo)

      wx.showModal({
        title: 'API调试信息',
        content: JSON.stringify(debugInfo, null, 2),
        showCancel: false,
        confirmText: '确定'
      })

    } catch (error) {
      console.error('🔍 API调试失败:', error)
      wx.showModal({
        title: 'API调试错误',
        content: `错误: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      })
    }
  },

  /**
   * 获取设备状态样式类
   */
  getDeviceStatusClass(status) {
    switch (status) {
      case 'online':
      case '在线':
      case 1:
        return 'device-status--online'
      case 'offline':
      case '离线':
      case 0:
        return 'device-status--offline'
      case 'warning':
      case '告警':
      case 2:
        return 'device-status--warning'
      case 'error':
      case '故障':
      case 3:
        return 'device-status--error'
      default:
        return 'device-status--offline'
    }
  },

  /**
   * 获取设备状态文本
   */
  getDeviceStatusText(status) {
    switch (status) {
      case 'online':
      case 1:
        return '在线'
      case 'offline':
      case 0:
        return '离线'
      case 'warning':
      case 2:
        return '告警'
      case 'error':
      case 3:
        return '故障'
      default:
        return '未知'
    }
  }
})