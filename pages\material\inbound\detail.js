// pages/material/inbound/detail.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { getInboundDetail, submitInbound, handleInbound, auditInbound, deleteInbound } from '../../../api/material.js'

Page({
  data: {
    inboundId: '',
    inboundDetail: null,
    loading: true,
    
    // 操作权限
    canSubmit: false,
    canHandle: false,
    canAudit: false,
    canDelete: false,
    canEdit: false
  },

  onLoad(options) {
    console.log('📋 入库单详情页面加载，参数:', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取入库单ID
    const { inboundId } = options
    if (!inboundId) {
      console.error('❌ 入库单ID为空')
      Toast.fail('入库单ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('✅ 入库单ID:', inboundId)
    this.setData({ inboundId })
    
    // 加载入库单详情
    this.loadInboundDetail()
  },

  /**
   * 加载入库单详情
   */
  async loadInboundDetail() {
    try {
      this.setData({ loading: true })
      
      console.log('📋 开始加载入库单详情:', this.data.inboundId)
      
      // 调用API获取入库单详情
      const response = await getInboundDetail(this.data.inboundId)
      
      if (response && response.code === 200 && response.data) {
        const inboundDetail = response.data
        
        // 更新页面标题
        wx.setNavigationBarTitle({
          title: `入库单: ${inboundDetail.inboundId}`
        })
        
        // 设置操作权限
        this.setOperationPermissions(inboundDetail.status)
        
        this.setData({
          inboundDetail: inboundDetail
        })
        
        console.log('✅ 入库单详情加载成功:', inboundDetail)
      } else {
        throw new Error(response?.msg || '获取入库单详情失败')
      }
    } catch (error) {
      console.error('❌ 加载入库单详情失败:', error)
      
      // 显示错误提示
      Toast.fail(error.message || '加载失败')
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 设置操作权限
   */
  setOperationPermissions(status) {
    // 根据入库单状态设置操作权限
    // 1-草稿, 2-待确认, 3-待审核, 4-已通过, 5-已退回
    const canSubmit = status === 1 || status === 5  // 草稿或已退回可提交
    const canHandle = status === 2                  // 待确认可确认
    const canAudit = status === 3                   // 待审核可审核
    const canDelete = status === 1 || status === 5  // 草稿或已退回可删除
    const canEdit = status === 1 || status === 5    // 草稿或已退回可编辑
    
    this.setData({
      canSubmit,
      canHandle,
      canAudit,
      canDelete,
      canEdit
    })
  },

  /**
   * 提交入库单
   */
  onSubmitInbound() {
    Dialog.confirm({
      title: '提交确认',
      message: '提交后将进入审核流程，确定要提交吗？',
      confirmButtonText: '确定提交',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        Toast.loading('提交中...')
        
        // 调用API提交入库单
        const response = await submitInbound(this.data.inboundId)
        
        if (response && response.code === 200) {
          Toast.success('提交成功')
          
          // 重新加载详情
          this.loadInboundDetail()
        } else {
          throw new Error(response?.msg || '提交失败')
        }
      } catch (error) {
        console.error('❌ 提交入库单失败:', error)
        Toast.fail(error.message || '提交失败')
      }
    }).catch(() => {
      // 取消提交
    })
  },

  /**
   * 确认入库单
   */
  onHandleInbound() {
    Dialog.confirm({
      title: '确认入库',
      message: '确认后将进入审核阶段，确定要确认吗？',
      confirmButtonText: '确定确认',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        Toast.loading('确认中...')
        
        // 调用API确认入库单
        const response = await handleInbound(this.data.inboundId)
        
        if (response && response.code === 200) {
          Toast.success('确认成功')
          
          // 重新加载详情
          this.loadInboundDetail()
        } else {
          throw new Error(response?.msg || '确认失败')
        }
      } catch (error) {
        console.error('❌ 确认入库单失败:', error)
        Toast.fail(error.message || '确认失败')
      }
    }).catch(() => {
      // 取消确认
    })
  },

  /**
   * 审核通过入库单
   */
  onAuditPassInbound() {
    Dialog.confirm({
      title: '审核通过',
      message: '审核通过后将执行入库操作，确定要通过吗？',
      confirmButtonText: '确定通过',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        Toast.loading('审核中...')
        
        // 调用API审核入库单
        const response = await auditInbound(this.data.inboundId, 4)
        
        if (response && response.code === 200) {
          Toast.success('审核通过')
          
          // 重新加载详情
          this.loadInboundDetail()
        } else {
          throw new Error(response?.msg || '审核失败')
        }
      } catch (error) {
        console.error('❌ 审核入库单失败:', error)
        Toast.fail(error.message || '审核失败')
      }
    }).catch(() => {
      // 取消审核
    })
  },

  /**
   * 审核退回入库单
   */
  onAuditRejectInbound() {
    Dialog.confirm({
      title: '审核退回',
      message: '审核退回后需要重新提交，确定要退回吗？',
      confirmButtonText: '确定退回',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        Toast.loading('审核中...')
        
        // 调用API审核入库单
        const response = await auditInbound(this.data.inboundId, 5)
        
        if (response && response.code === 200) {
          Toast.success('已退回')
          
          // 重新加载详情
          this.loadInboundDetail()
        } else {
          throw new Error(response?.msg || '审核失败')
        }
      } catch (error) {
        console.error('❌ 审核入库单失败:', error)
        Toast.fail(error.message || '审核失败')
      }
    }).catch(() => {
      // 取消审核
    })
  },

  /**
   * 删除入库单
   */
  onDeleteInbound() {
    Dialog.confirm({
      title: '删除确认',
      message: '删除后无法恢复，确定要删除吗？',
      confirmButtonText: '确定删除',
      confirmButtonColor: '#ee0a24',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        Toast.loading('删除中...')
        
        // 调用API删除入库单
        const response = await deleteInbound(this.data.inboundId)
        
        if (response && response.code === 200) {
          Toast.success('删除成功')
          
          // 返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1000)
        } else {
          throw new Error(response?.msg || '删除失败')
        }
      } catch (error) {
        console.error('❌ 删除入库单失败:', error)
        Toast.fail(error.message || '删除失败')
      }
    }).catch(() => {
      // 取消删除
    })
  },

  /**
   * 编辑入库单
   */
  onEditInbound() {
    wx.navigateTo({
      url: `/pages/material/inbound/edit?inboundId=${this.data.inboundId}`,
      fail: () => {
        Toast.fail('编辑功能开发中')
      }
    })
  }
})
