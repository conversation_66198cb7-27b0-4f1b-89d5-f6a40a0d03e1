/**
 * 设备监控系统 - 认证工具
 * Token管理、用户信息存储等认证相关功能
 * 创建时间: 2025-01-07
 */

// 存储键名常量
const STORAGE_KEYS = {
  TOKEN: 'device_monitor_token',
  USER_INFO: 'device_monitor_user_info',
  OPENID: 'device_monitor_openid',
  LOGIN_TIME: 'device_monitor_login_time'
}

/**
 * 获取Token
 */
function getToken() {
  try {
    return wx.getStorageSync(STORAGE_KEYS.TOKEN) || ''
  } catch (e) {
    console.error('获取Token失败:', e)
    return ''
  }
}

/**
 * 设置Token
 */
function setToken(token) {
  try {
    wx.setStorageSync(STORAGE_KEYS.TOKEN, token)
    // 记录登录时间
    wx.setStorageSync(STORAGE_KEYS.LOGIN_TIME, Date.now())
    return true
  } catch (e) {
    console.error('设置Token失败:', e)
    return false
  }
}

/**
 * 移除Token
 */
function removeToken() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.TOKEN)
    wx.removeStorageSync(STORAGE_KEYS.LOGIN_TIME)
    return true
  } catch (e) {
    console.error('移除Token失败:', e)
    return false
  }
}

/**
 * 检查Token是否存在
 */
function hasToken() {
  const token = getToken()
  return token && token.length > 0
}

/**
 * 检查Token是否过期
 * 根据后端文档，Token有效期为30分钟
 */
function isTokenExpired() {
  try {
    const loginTime = wx.getStorageSync(STORAGE_KEYS.LOGIN_TIME)
    if (!loginTime) {
      return true
    }

    const now = Date.now()
    const expireTime = 30 * 60 * 1000 // 30分钟
    
    return (now - loginTime) > expireTime
  } catch (e) {
    console.error('检查Token过期失败:', e)
    return true
  }
}

/**
 * 解析JWT Token
 * 获取Token中的用户信息（仅用于调试，生产环境应从后端获取）
 */
function parseToken(token) {
  try {
    if (!token) return null
    
    const parts = token.split('.')
    if (parts.length !== 3) return null
    
    const payload = parts[1]
    const decoded = JSON.parse(atob(payload))
    return decoded
  } catch (e) {
    console.error('解析Token失败:', e)
    return null
  }
}

/**
 * 获取用户信息
 */
function getUserInfo() {
  try {
    const userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO)
    return userInfo ? JSON.parse(userInfo) : null
  } catch (e) {
    console.error('获取用户信息失败:', e)
    return null
  }
}

/**
 * 设置用户信息
 */
function setUserInfo(userInfo) {
  try {
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo))
    return true
  } catch (e) {
    console.error('设置用户信息失败:', e)
    return false
  }
}

/**
 * 移除用户信息
 */
function removeUserInfo() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.USER_INFO)
    return true
  } catch (e) {
    console.error('移除用户信息失败:', e)
    return false
  }
}

/**
 * 获取微信OpenID
 */
function getOpenId() {
  try {
    return wx.getStorageSync(STORAGE_KEYS.OPENID) || ''
  } catch (e) {
    console.error('获取OpenID失败:', e)
    return ''
  }
}

/**
 * 设置微信OpenID
 */
function setOpenId(openid) {
  try {
    wx.setStorageSync(STORAGE_KEYS.OPENID, openid)
    return true
  } catch (e) {
    console.error('设置OpenID失败:', e)
    return false
  }
}

/**
 * 移除微信OpenID
 */
function removeOpenId() {
  try {
    wx.removeStorageSync(STORAGE_KEYS.OPENID)
    return true
  } catch (e) {
    console.error('移除OpenID失败:', e)
    return false
  }
}

/**
 * 检查是否已登录
 */
function isLoggedIn() {
  const token = getToken()
  if (!token) {
    return false
  }

  // 检查Token是否过期
  if (isTokenExpired()) {
    // Token过期，清除相关信息
    logout()
    return false
  }

  return true
}

/**
 * 检查是否需要绑定账号
 * 有OpenID但没有Token，说明需要绑定系统账号
 */
function needBindAccount() {
  const openid = getOpenId()
  const token = getToken()
  return openid && !token
}

/**
 * 登出
 * 清除所有认证相关信息
 */
function logout() {
  try {
    removeToken()
    removeUserInfo()
    // 保留OpenID，用于重新绑定
    // removeOpenId()
    return true
  } catch (e) {
    console.error('登出失败:', e)
    return false
  }
}

/**
 * 完全清除
 * 清除包括OpenID在内的所有信息
 */
function clearAll() {
  try {
    removeToken()
    removeUserInfo()
    removeOpenId()
    return true
  } catch (e) {
    console.error('清除所有信息失败:', e)
    return false
  }
}

/**
 * 获取登录状态信息
 */
function getAuthStatus() {
  const token = getToken()
  const userInfo = getUserInfo()
  const openid = getOpenId()
  const loginTime = wx.getStorageSync(STORAGE_KEYS.LOGIN_TIME)

  return {
    hasToken: !!token,
    hasUserInfo: !!userInfo,
    hasOpenId: !!openid,
    isLoggedIn: isLoggedIn(),
    needBindAccount: needBindAccount(),
    isTokenExpired: isTokenExpired(),
    loginTime: loginTime,
    token: token,
    userInfo: userInfo,
    openid: openid
  }
}

/**
 * 刷新Token时间
 * 当用户有活动时调用，延长Token有效期
 */
function refreshTokenTime() {
  try {
    if (hasToken()) {
      wx.setStorageSync(STORAGE_KEYS.LOGIN_TIME, Date.now())
      return true
    }
    return false
  } catch (e) {
    console.error('刷新Token时间失败:', e)
    return false
  }
}

/**
 * 自动刷新Token
 * 根据后端文档，20分钟内有活动会自动延期
 */
function autoRefreshToken() {
  try {
    const loginTime = wx.getStorageSync(STORAGE_KEYS.LOGIN_TIME)
    if (!loginTime) return false

    const now = Date.now()
    const refreshThreshold = 20 * 60 * 1000 // 20分钟

    // 如果距离登录时间超过20分钟但未超过30分钟，则刷新
    if ((now - loginTime) > refreshThreshold && !isTokenExpired()) {
      return refreshTokenTime()
    }

    return false
  } catch (e) {
    console.error('自动刷新Token失败:', e)
    return false
  }
}

// 导出方法
export {
  // Token管理
  getToken,
  setToken,
  removeToken,
  hasToken,
  isTokenExpired,
  parseToken,
  
  // 用户信息管理
  getUserInfo,
  setUserInfo,
  removeUserInfo,
  
  // OpenID管理
  getOpenId,
  setOpenId,
  removeOpenId,
  
  // 登录状态管理
  isLoggedIn,
  needBindAccount,
  logout,
  clearAll,
  getAuthStatus,
  
  // Token刷新
  refreshTokenTime,
  autoRefreshToken,
  
  // 常量
  STORAGE_KEYS
}
