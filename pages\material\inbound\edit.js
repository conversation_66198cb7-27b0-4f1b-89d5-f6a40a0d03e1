// pages/material/inbound/edit.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'

Page({
  data: {
    inboundId: ''
  },

  onLoad(options) {
    console.log('📋 编辑入库单页面加载，参数:', options)
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取入库单ID
    const { inboundId } = options
    if (!inboundId) {
      console.error('❌ 入库单ID为空')
      Toast.fail('入库单ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('✅ 入库单ID:', inboundId)
    this.setData({ inboundId })
    
    Toast.success('编辑入库单功能开发中')
  }
})
