/**
 * 设备监控系统 - HTTP请求封装
 * 基于微信小程序的网络请求API
 * 创建时间: 2025-01-07
 */

// 引入Token管理工具
import { getToken, removeToken } from './auth.js'
// 引入环境配置
import { getApiBaseUrl, isDebugMode } from '../config/env.js'

// 配置常量
const CONFIG = {
  // 后端API基础地址 - 从环境配置获取
  BASE_URL: getApiBaseUrl(),
  // 请求超时时间
  TIMEOUT: 10000,
  // 重试次数
  RETRY_COUNT: 3,
  // 调试模式
  DEBUG: isDebugMode()
}

/**
 * 显示加载提示
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title: title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading()
}

/**
 * 显示错误提示
 */
function showError(message) {
  wx.showToast({
    title: message,
    icon: 'error',
    duration: 2000
  })
}

/**
 * 显示成功提示
 */
function showSuccess(message) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 处理HTTP错误状态码
 */
function handleHttpError(statusCode, data) {
  switch (statusCode) {
    case 401:
      // 未授权，清除Token并跳转登录
      removeToken()
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return '登录已过期，请重新登录'
    case 403:
      return '权限不足，无法访问'
    case 404:
      return '请求的资源不存在'
    case 500:
      return '服务器内部错误'
    case 502:
      return '网关错误'
    case 503:
      return '服务暂时不可用'
    default:
      return data?.msg || `请求失败 (${statusCode})`
  }
}

/**
 * 处理业务错误码
 */
function handleBusinessError(code, msg) {
  switch (code) {
    case 401:
      // 业务层未授权
      removeToken()
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return '登录已过期，请重新登录'
    case 403:
      return '权限不足'
    case 500:
      return '系统异常'
    case 601:
      // 警告信息，不显示错误提示
      return null
    default:
      return msg || '操作失败'
  }
}

/**
 * 核心请求方法
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      showLoading: needLoading = true,
      showError: needShowError = true,
      timeout = CONFIG.TIMEOUT
    } = options

    // 显示加载提示
    if (needLoading) {
      showLoading()
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : CONFIG.BASE_URL + url

    // 构建请求头
    const requestHeader = {
      'Content-Type': 'application/json',
      ...header
    }

    // 添加Token到请求头
    const token = getToken()
    if (token) {
      requestHeader['Authorization'] = `Bearer ${token}`
    }

    // 调试日志
    if (CONFIG.DEBUG) {
      console.log('🚀 发起请求:', {
        url: fullUrl,
        method: method.toUpperCase(),
        data: data,
        header: requestHeader
      })
    }

    // 发起请求
    wx.request({
      url: fullUrl,
      method: method.toUpperCase(),
      data: data,
      header: requestHeader,
      timeout: timeout,
      success: (res) => {
        if (needLoading) {
          hideLoading()
        }

        const { statusCode, data: responseData } = res

        // HTTP状态码检查
        if (statusCode >= 200 && statusCode < 300) {
          // 业务状态码检查
          if (responseData && typeof responseData === 'object') {
            const { code, msg, data: businessData } = responseData

            // 如果有code字段，按RuoYi格式处理
            if (code !== undefined) {
              if (code === 200) {
                // 成功
                resolve(responseData)
              } else {
                // 业务错误
                const errorMsg = handleBusinessError(code, msg)
                if (errorMsg && needShowError) {
                  showError(errorMsg)
                }
                reject(new Error(errorMsg || msg))
              }
            } else {
              // 没有code字段，直接返回数据（如MyBatis Plus分页格式）
              resolve(responseData)
            }
          } else {
            // 非JSON响应，直接返回
            resolve(responseData)
          }
        } else {
          // HTTP错误
          const errorMsg = handleHttpError(statusCode, responseData)
          if (needShowError) {
            showError(errorMsg)
          }
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        if (needLoading) {
          hideLoading()
        }

        console.error('Request failed:', {
          url: fullUrl,
          method: method.toUpperCase(),
          error: err
        })

        let errorMsg = '网络请求失败'
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接'
          } else if (err.errMsg.includes('fail')) {
            errorMsg = '网络连接失败，请检查网络设置'
          } else if (err.errMsg.includes('url not in domain list')) {
            errorMsg = '请求域名未配置，请检查服务器地址'
          }
        }

        if (needShowError) {
          showError(errorMsg)
        }
        reject(new Error(errorMsg))
      }
    })
  })
}

/**
 * GET请求
 */
function get(url, params = {}, options = {}) {
  // 将参数拼接到URL
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    url += (url.includes('?') ? '&' : '?') + queryString
  }

  return request({
    url,
    method: 'GET',
    ...options
  })
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 */
function del(url, options = {}) {
  return request({
    url,
    method: 'DELETE',
    ...options
  })
}

/**
 * 文件上传
 */
function upload(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const {
      name = 'file',
      formData = {},
      header = {},
      showLoading: needLoading = true
    } = options

    if (needLoading) {
      showLoading('上传中...')
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : CONFIG.BASE_URL + url

    // 构建请求头
    const requestHeader = { ...header }

    // 添加Token到请求头
    const token = getToken()
    if (token) {
      requestHeader['Authorization'] = `Bearer ${token}`
    }

    wx.uploadFile({
      url: fullUrl,
      filePath: filePath,
      name: name,
      formData: formData,
      header: requestHeader,
      success: (res) => {
        if (needLoading) {
          hideLoading()
        }

        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data)
          } else {
            const errorMsg = handleBusinessError(data.code, data.msg)
            if (errorMsg) {
              showError(errorMsg)
            }
            reject(new Error(errorMsg || data.msg))
          }
        } catch (e) {
          showError('上传失败')
          reject(new Error('上传失败'))
        }
      },
      fail: (err) => {
        if (needLoading) {
          hideLoading()
        }
        showError('上传失败')
        reject(err)
      }
    })
  })
}

// 导出方法
export {
  request,
  get,
  post,
  put,
  del,
  upload,
  showLoading,
  hideLoading,
  showError,
  showSuccess,
  CONFIG
}
