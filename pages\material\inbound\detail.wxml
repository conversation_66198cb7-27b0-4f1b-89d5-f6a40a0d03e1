<!--pages/material/inbound/detail.wxml-->
<view class="page-container">
  
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <scroll-view class="detail-content" scroll-y="true" wx:if="{{ !loading && inboundDetail }}">
    
    <!-- 入库单基本信息 -->
    <view class="section-container">
      <view class="section-header">
        <view class="header-content">
          <view class="header-info">
            <text class="inbound-id">{{ inboundDetail.inboundId }}</text>
            <view class="inbound-tags">
              <van-tag
                type="{{ inboundDetail.inboundType === 1 ? 'primary' : (inboundDetail.inboundType === 2 ? 'warning' : 'default') }}"
                size="mini"
              >
                {{ inboundDetail.inboundTypeName }}
              </van-tag>
              <van-tag
                type="{{ inboundDetail.status === 1 ? 'default' : (inboundDetail.status === 2 ? 'warning' : (inboundDetail.status === 3 ? 'primary' : (inboundDetail.status === 4 ? 'success' : 'danger'))) }}"
                size="mini"
              >
                {{ inboundDetail.statusName }}
              </van-tag>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="section-container">
      <view class="section-title">
        <van-icon name="info-o" size="16" />
        <text>基本信息</text>
      </view>
      
      <van-cell-group>
        <van-cell title="业务日期" value="{{ inboundDetail.businessDate }}" />
        <van-cell title="供应商" value="{{ inboundDetail.supplierName }}" />
        <van-cell title="入库类型" value="{{ inboundDetail.inboundTypeName }}" />
        <van-cell title="总金额" value="¥{{ inboundDetail.totalAmount }}" />
        <van-cell title="物品数量" value="{{ inboundDetail.itemCount }} 种" />
        <van-cell title="备注" value="{{ inboundDetail.remark || '无' }}" />
      </van-cell-group>
    </view>

    <!-- 流程信息 -->
    <view class="section-container">
      <view class="section-title">
        <van-icon name="clock-o" size="16" />
        <text>流程信息</text>
      </view>
      
      <van-cell-group>
        <van-cell title="制单人" value="{{ inboundDetail.creatorName }}" />
        <van-cell title="创建时间" value="{{ inboundDetail.createTime }}" />
        <van-cell 
          title="经手人" 
          value="{{ inboundDetail.handlerName || '待确认' }}" 
          wx:if="{{ inboundDetail.status >= 3 }}"
        />
        <van-cell 
          title="确认时间" 
          value="{{ inboundDetail.handleTime || '待确认' }}" 
          wx:if="{{ inboundDetail.status >= 3 }}"
        />
        <van-cell 
          title="审核人" 
          value="{{ inboundDetail.auditorName || '待审核' }}" 
          wx:if="{{ inboundDetail.status >= 4 }}"
        />
        <van-cell 
          title="审核时间" 
          value="{{ inboundDetail.auditTime || '待审核' }}" 
          wx:if="{{ inboundDetail.status >= 4 }}"
        />
      </van-cell-group>
    </view>

    <!-- 入库明细 -->
    <view class="section-container">
      <view class="section-title">
        <van-icon name="records" size="16" />
        <text>入库明细</text>
      </view>
      
      <view class="detail-placeholder">
        <text>入库明细功能开发中...</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section" wx:if="{{ canSubmit || canHandle || canAudit || canEdit || canDelete }}">
      <van-divider>操作</van-divider>
      
      <view class="action-buttons">
        <!-- 提交按钮 -->
        <van-button
          wx:if="{{ canSubmit }}"
          type="primary"
          size="large"
          bind:click="onSubmitInbound"
          custom-class="action-button"
        >
          提交
        </van-button>
        
        <!-- 确认按钮 -->
        <van-button
          wx:if="{{ canHandle }}"
          type="warning"
          size="large"
          bind:click="onHandleInbound"
          custom-class="action-button"
        >
          确认
        </van-button>
        
        <!-- 审核按钮 -->
        <view wx:if="{{ canAudit }}" class="audit-buttons">
          <van-button
            type="success"
            size="large"
            bind:click="onAuditPassInbound"
            custom-class="action-button audit-pass"
          >
            审核通过
          </van-button>
          <van-button
            type="danger"
            size="large"
            bind:click="onAuditRejectInbound"
            custom-class="action-button audit-reject"
          >
            审核退回
          </van-button>
        </view>
        
        <!-- 编辑按钮 -->
        <van-button
          wx:if="{{ canEdit }}"
          type="info"
          size="large"
          bind:click="onEditInbound"
          custom-class="action-button"
        >
          编辑
        </van-button>
        
        <!-- 删除按钮 -->
        <van-button
          wx:if="{{ canDelete }}"
          type="danger"
          size="large"
          bind:click="onDeleteInbound"
          custom-class="action-button"
        >
          删除
        </van-button>
      </view>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{ !loading && !inboundDetail }}">
    <van-empty description="未找到入库单信息" image="search" />
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
