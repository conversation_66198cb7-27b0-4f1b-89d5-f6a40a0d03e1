<!--pages/device/detail.wxml-->
<view class="device-detail-page">

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading type="spinner" size="24" text-size="14">加载中...</van-loading>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{hasError}}" class="error-container">
    <van-empty
      image="error"
      description="加载失败"
    >
      <van-button
        round
        type="primary"
        size="small"
        bind:click="onRetryLoad"
      >
        重新加载
      </van-button>
    </van-empty>
  </view>

  <!-- 设备详情内容 -->
  <view wx:else class="detail-content">

    <!-- 设备基本信息 -->
    <van-cell-group title="基本信息" custom-class="info-group">
      <van-cell 
        title="设备名称" 
        value="{{deviceInfo.deviceName}}" 
        label="设备的显示名称"
      />
      <van-cell 
        title="设备ID" 
        value="{{deviceInfo.id}}" 
        label="设备的唯一标识符"
      />
      <van-cell 
        title="设备状态" 
        label="当前设备的运行状态"
      >
        <van-tag 
          slot="right-icon"
          type="{{getDeviceStatusClass(deviceInfo.deviceStatus)}}"
          size="medium"
        >
          {{getDeviceStatusText(deviceInfo.deviceStatus)}}
        </van-tag>
      </van-cell>
      <van-cell 
        title="设备型号" 
        value="{{deviceInfo.deviceModel || '未设置'}}" 
        label="设备的型号规格"
      />
      <van-cell 
        title="制造商" 
        value="{{deviceInfo.manufacturer || '未设置'}}" 
        label="设备制造厂商"
      />
      <van-cell 
        title="序列号" 
        value="{{deviceInfo.serialNumber || '未设置'}}" 
        label="设备出厂序列号"
      />
    </van-cell-group>

    <!-- 网络信息 -->
    <van-cell-group title="网络信息" custom-class="info-group">
      <van-cell 
        title="IP地址" 
        value="{{deviceInfo.ipAddress || '未设置'}}" 
        label="设备的网络IP地址"
      />
      <van-cell 
        title="端口号" 
        value="{{deviceInfo.devicePort || '未设置'}}" 
        label="设备的通信端口"
      />
    </van-cell-group>

    <!-- 位置信息 -->
    <van-cell-group title="位置信息" custom-class="info-group">
      <van-cell 
        title="安装位置" 
        value="{{deviceInfo.location || '未设置'}}" 
        label="设备的物理安装位置"
      />
      <van-cell 
        title="安装日期" 
        value="{{formatDate(deviceInfo.installationDate)}}" 
        label="设备的安装时间"
      />
    </van-cell-group>

    <!-- 设备图片 -->
    <van-cell-group 
      wx:if="{{pictureUrls.length > 0}}" 
      title="设备图片" 
      custom-class="info-group"
    >
      <view class="image-gallery">
        <van-grid column-num="3" border="{{false}}" gutter="8">
          <van-grid-item
            wx:for="{{pictureUrls}}"
            wx:key="index"
            use-slot
            bind:click="onPreviewImage"
            data-url="{{item}}"
            custom-class="image-item"
          >
            <van-image
              src="{{item}}"
              fit="cover"
              width="100%"
              height="120rpx"
              radius="8rpx"
              loading-icon="photo"
              error-icon="photo-fail"
            />
          </van-grid-item>
        </van-grid>
      </view>
    </van-cell-group>

    <!-- 操作按钮 - 只有在线设备才显示 -->
    <view wx:if="{{deviceInfo.deviceStatus === 1}}" class="action-section">
      <van-button
        type="primary"
        size="large"
        round
        custom-class="action-btn"
        bind:click="onViewRealTimeData"
      >
        查看实时数据
      </van-button>
    </view>

    <!-- 离线设备提示 -->
    <view wx:else class="offline-tip-section">
      <view class="offline-tip">
        <van-icon name="info-o" size="16" color="#969799" />
        <text class="tip-text">设备离线，无法查看实时数据</text>
      </view>
    </view>

  </view>

</view>
