<!--pages/index/index.wxml-->
<view class="page-container">

  <!-- 顶部通知栏 -->
  <van-notice-bar
    wx:if="{{noticeText}}"
    text="{{noticeText}}"
    mode="closeable"
    bind:close="onNoticeClose"
    color="#1989fa"
    background="#e6f4ff"
  />

  <!-- 主要内容区域 -->
  <view class="content-container">

    <!-- 用户欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-info">
        <view class="welcome-text">
          <text class="welcome-title">{{welcomeTitle}}</text>
          <text class="welcome-subtitle">{{welcomeSubtitle}}</text>
        </view>
        <view class="user-avatar" bind:tap="goToProfile">
          <image src="{{userInfo.avatar || '/images/default-avatar.png'}}" class="avatar-img" />
        </view>
      </view>
    </view>

    <!-- 功能宫格 -->
    <view class="grid-section">
      <view class="section-title">
        <text class="title-text">功能导航</text>
      </view>

      <van-grid column-num="1" border="{{false}}" gutter="16">
        <van-grid-item
          wx:for="{{gridItems}}"
          wx:key="id"
          use-slot
          bind:click="onGridItemClick"
          data-item="{{item}}"
          custom-class="grid-item"
        >
          <view class="grid-content">
            <view class="grid-icon {{item.iconClass}}">
              <van-icon name="{{item.icon}}" size="24" />
            </view>
            <text class="grid-text">{{item.text}}</text>
            <van-tag
              wx:if="{{item.badge}}"
              type="danger"
              size="mini"
              custom-class="grid-badge"
            >
              {{item.badge}}
            </van-tag>
          </view>
        </van-grid-item>
      </van-grid>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <view class="section-title">
        <text class="title-text">快速操作</text>
      </view>

      <van-grid column-num="2" border="{{false}}" gutter="16">
        <van-grid-item
          wx:for="{{quickActions}}"
          wx:key="id"
          bind:click="onQuickActionClick"
          data-action="{{item}}"
          custom-class="action-item"
        >
          <view class="action-content">
            <van-icon name="{{item.icon}}" size="20" color="{{item.color}}" />
            <text class="action-text">{{item.text}}</text>
          </view>
        </van-grid-item>
      </van-grid>
    </view>

    <!-- 环境信息（仅开发环境） -->
    <view class="debug-section" wx:if="{{showEnvInfo}}">
      <van-divider content-position="center">开发调试</van-divider>
      <view class="debug-info">
        <text class="debug-text">环境: {{envConfig.ENV_NAME}}</text>
        <text class="debug-text">API: {{envConfig.API_BASE_URL}}</text>
        <view class="debug-actions">
          <van-tag type="primary" size="mini" bind:tap="onEnvSwitch">切换环境</van-tag>
          <van-tag type="default" size="mini" bind:tap="testConnection">测试连接</van-tag>
        </view>
      </view>
    </view>

  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
