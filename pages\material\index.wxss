/* pages/material/index.wxss */
.page-container {
  padding: 32rpx 16rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.header-section {
  text-align: center;
  margin-bottom: 48rpx;
  padding: 32rpx 0;
}

.header-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  color: #999;
}

/* 菜单区域 */
.menu-section {
  margin-bottom: 32rpx;
  padding: 0 16rpx;
}

.menu-cell {
  border-radius: 16rpx !important;
  margin-bottom: 16rpx !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
  overflow: hidden !important;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
