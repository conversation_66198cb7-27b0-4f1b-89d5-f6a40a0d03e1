/* pages/material/stocktaking/list.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
}

/* 列表区域 */
.list-section {
  flex: 1;
  height: calc(100vh - 120rpx);
}

.scroll-container {
  height: 100%;
  padding: 0 16rpx;
}

/* 重写 van-cell 样式 */
.stocktaking-item {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.item-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  width: 100%;
}

.item-main {
  flex: 1;
  min-width: 0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.item-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.stocktaking-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.item-code {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
}

.item-info {
  margin-bottom: 16rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
  min-width: 120rpx;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.footer-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.create-time {
  font-size: 24rpx;
  color: #999;
}

.creator {
  font-size: 24rpx;
  color: #999;
}

.footer-actions {
  display: flex;
  gap: 16rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx;
  font-size: 24rpx;
  color: #999;
}

.load-finished {
  color: #999;
}

/* 空状态 */
.van-empty {
  padding: 80rpx 32rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .item-content {
    padding: 20rpx;
    gap: 20rpx;
  }

  .stocktaking-name {
    font-size: 30rpx;
  }

  .info-row {
    font-size: 24rpx;
  }

  .info-label {
    min-width: 100rpx;
  }

  .item-code {
    font-size: 22rpx;
  }

  .create-time,
  .creator {
    font-size: 22rpx;
  }
}

/* 状态标签样式调整 */
.van-tag {
  border-radius: 8rpx !important;
  font-size: 20rpx !important;
  padding: 4rpx 8rpx !important;
}

/* 按钮样式调整 */
.van-button--mini {
  height: 56rpx !important;
  line-height: 54rpx !important;
  font-size: 20rpx !important;
  padding: 0 16rpx !important;
}

/* 搜索框样式调整 */
.van-search {
  padding: 16rpx 0 !important;
}

.van-search__content {
  border-radius: 16rpx !important;
}

/* 加载动画样式 */
.van-loading {
  color: #1989fa !important;
}

.van-loading__text {
  color: #999 !important;
  font-size: 24rpx !important;
  margin-left: 16rpx !important;
}