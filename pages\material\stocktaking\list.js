// pages/material/stocktaking/list.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { getStocktakingList, generateStocktakingDetails, startStocktaking } from '../../../api/material.js'

Page({
  data: {
    // 搜索条件
    searchKeyword: '',
    
    // 盘点计划列表
    stocktakingList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 10,
      total: 0,
      pages: 0
    },
    
    // 加载状态
    loading: false,
    finished: false,
    refreshing: false,
    
    // 状态映射
    statusMap: {
      0: { label: '草稿', color: '#969799' },
      1: { label: '进行中', color: '#ff976a' },
      2: { label: '已完成', color: '#1989fa' },
      3: { label: '已审核', color: '#07c160' }
    },
    
    // 盘点类型映射
    typeMap: {
      1: '全盘',
      2: '抽盘',
      3: '循环盘点'
    }
  },

  onLoad() {
    console.log('📋 库存盘点列表页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    console.log('📋 初始化库存盘点列表页面')
    this.loadStocktakingList()
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      // 重置分页信息
      this.setData({
        'pageInfo.current': 1,
        stocktakingList: [],
        finished: false
      })
      
      // 重新加载数据
      await this.loadStocktakingList(true)
      
      Toast.success('数据已更新')
    } catch (error) {
      console.error('刷新数据失败:', error)
      Toast.fail('刷新失败')
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true })
    
    this.refreshData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 上拉加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished) {
      return
    }
    
    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    this.loadStocktakingList()
  },

  /**
   * 加载盘点计划列表
   */
  async loadStocktakingList(isRefresh = false) {
    if (this.data.loading) return
    
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size
      }

      // 添加搜索条件
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        const keyword = this.data.searchKeyword.trim()
        // 根据关键词特征判断搜索类型
        if (/^[A-Z0-9]+$/i.test(keyword)) {
          // 如果是字母数字组合，按盘点单号搜索
          params.stocktakingCode = keyword
        } else {
          // 否则按盘点名称搜索
          params.stocktakingName = keyword
        }
      }

      console.log('📋 盘点计划查询参数:', params)

      // 调用后端接口获取盘点计划数据
      const response = await getStocktakingList(params)
      
      if (response && response.code === 200 && response.data) {
        const { records, total, size, current, pages } = response.data
        
        console.log('📋 获取到盘点计划列表:', records)
        
        // 更新列表数据
        let newList = []
        if (isRefresh || this.data.pageInfo.current === 1) {
          // 刷新或首次加载，替换数据
          newList = records || []
        } else {
          // 加载更多，追加数据
          newList = [...this.data.stocktakingList, ...(records || [])]
        }
        
        // 更新页面数据
        this.setData({
          stocktakingList: newList,
          'pageInfo.total': total || 0,
          'pageInfo.size': size || 10,
          'pageInfo.current': current || 1,
          'pageInfo.pages': pages || 0,
          finished: (current >= pages) || (records && records.length === 0)
        })
        
        console.log('✅ 盘点计划列表加载完成，共', newList.length, '条记录')
      } else {
        throw new Error(response?.msg || '获取盘点计划列表失败')
      }
    } catch (error) {
      console.error('❌ 加载盘点计划列表失败:', error)
      
      // 如果是首次加载失败，显示错误信息
      if (this.data.pageInfo.current === 1) {
        Toast.fail('获取数据失败')
      }
      
      // 恢复页码
      if (this.data.pageInfo.current > 1) {
        this.setData({
          'pageInfo.current': this.data.pageInfo.current - 1
        })
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const value = event.detail
    this.setData({
      searchKeyword: value
    })
    
    // 防抖搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
    
    this.searchTimer = setTimeout(() => {
      if (value !== this.data.searchKeyword) return
      this.performSearch(value)
    }, 500)
  },

  /**
   * 搜索确认
   */
  onSearch(event) {
    const keyword = event.detail || this.data.searchKeyword
    this.performSearch(keyword)
  },

  /**
   * 执行搜索
   */
  performSearch(keyword) {
    console.log('🔍 执行盘点计划搜索，关键词:', keyword)
    
    // 重置分页信息
    this.setData({
      'pageInfo.current': 1,
      stocktakingList: [],
      finished: false
    })
    
    // 重新加载数据
    this.loadStocktakingList(true)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  /**
   * 新增盘点计划
   */
  onAddStocktaking() {
    console.log('➕ 新增盘点计划')
    
    wx.navigateTo({
      url: '/pages/material/stocktaking/add',
      fail: () => {
        Toast.fail('新增盘点计划功能开发中')
      }
    })
  },

  /**
   * 盘点计划项点击
   */
  onStocktakingItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击盘点计划项:', item)
    
    // 显示加载提示
    Toast.loading('加载中...')
    
    // 跳转到盘点计划详情页面
    wx.navigateTo({
      url: `/pages/material/stocktaking/detail?stocktakingId=${item.stocktakingId}`,
      success: () => {
        Toast.clear()
      },
      fail: () => {
        Toast.clear()
        Toast.fail('盘点计划详情功能开发中')
      }
    })
  },

  /**
   * 生成明细
   */
  onGenerateDetails(event) {
    const item = event.currentTarget.dataset.item
    console.log('生成明细:', item)
    
    // 只有草稿状态可以生成明细
    if (item.status !== 0) {
      Toast.fail('当前状态不允许生成明细')
      return
    }
    
    Dialog.confirm({
      title: '生成明细确认',
      message: `确定要为盘点计划 ${item.stocktakingName} 生成明细吗？`,
      confirmButtonText: '确定生成',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        await generateStocktakingDetails(item.stocktakingId)
        Toast.success('明细生成成功')
        
        // 刷新列表
        this.refreshData()
      } catch (error) {
        console.error('生成明细失败:', error)
        Toast.fail(error.message || '生成明细失败')
      }
    }).catch(() => {
      // 取消生成
    })
  },

  /**
   * 开始盘点
   */
  onStartStocktaking(event) {
    const item = event.currentTarget.dataset.item
    console.log('开始盘点:', item)
    
    // 只有草稿状态可以开始盘点
    if (item.status !== 0) {
      Toast.fail('当前状态不允许开始盘点')
      return
    }
    
    Dialog.confirm({
      title: '开始盘点确认',
      message: `确定要开始盘点 ${item.stocktakingName} 吗？`,
      confirmButtonText: '确定开始',
      cancelButtonText: '取消'
    }).then(async () => {
      try {
        await startStocktaking(item.stocktakingId)
        Toast.success('盘点已开始')
        
        // 刷新列表
        this.refreshData()
      } catch (error) {
        console.error('开始盘点失败:', error)
        Toast.fail(error.message || '开始盘点失败')
      }
    }).catch(() => {
      // 取消开始
    })
  },

  /**
   * 页面卸载时清理定时器
   */
  onUnload() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})
