/* pages/material/inbound/add.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 表单区域 */
.form-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 24rpx 32rpx 16rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
}

.section-header .section-title {
  padding: 0;
  margin: 0;
}

/* 明细列表 */
.detail-list {
  padding: 0 16rpx;
}

.detail-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.detail-title {
  flex: 1;
}

.detail-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.detail-code {
  font-size: 24rpx;
  color: #666;
}

.detail-actions {
  padding: 8rpx;
}

.detail-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.detail-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.detail-spec {
  font-size: 26rpx;
  color: #666;
}

.detail-unit {
  font-size: 24rpx;
  color: #999;
}

.detail-warehouse {
  margin-bottom: 16rpx;
}

.detail-warehouse text {
  font-size: 26rpx;
  color: #666;
}

.detail-fields {
  /* 字段区域样式 */
}

/* 空状态 */
.empty-detail {
  text-align: center;
  padding: 80rpx 32rpx;
  color: #999;
  font-size: 28rpx;
}

/* 底部按钮 */
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.footer-buttons .van-button {
  flex: 1;
}

/* 选择器样式 */
.picker-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-search {
  padding: 16rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-content {
  flex: 1;
  overflow-y: auto;
  background: #f5f5f5;
}

/* 物品选择项样式 */
.item-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.item-code {
  font-size: 24rpx;
  color: #666;
}

.item-spec {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 24rpx;
  color: #999;
}

.item-unit {
  margin-top: 4rpx;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .section-title {
    font-size: 30rpx;
    padding: 20rpx 24rpx 12rpx;
  }

  .detail-item {
    padding: 20rpx;
  }

  .detail-name {
    font-size: 30rpx;
  }

  .footer-buttons {
    padding: 20rpx 24rpx;
    gap: 20rpx;
  }
}
