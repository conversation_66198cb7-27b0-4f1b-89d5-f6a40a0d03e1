/* pages/material/consumables/list.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 列表区域 */
.list-section {
  flex: 1;
  height: calc(100vh - 120rpx);
}

.scroll-container {
  height: 100%;
  padding: 0 16rpx;
}

/* 重写 van-cell 样式 */
.inventory-item {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.item-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  width: 100%;
}

.item-image {
  flex-shrink: 0;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  min-width: 0; /* 防止文字溢出 */
}

.item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16rpx;
}

.item-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-tags {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
  flex-wrap: wrap;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-code,
.item-spec {
  font-size: 26rpx;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
}

.stock-quantity {
  font-size: 28rpx;
  font-weight: 600;
  color: #07c160; /* 消耗品使用绿色 */
  flex: 1;
}

.safety-stock {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}

.item-warehouse {
  display: flex;
  align-items: center;
}

.warehouse-count {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态 */
.loading-container,
.finished-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx;
}

.finished-text {
  font-size: 24rpx;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .item-content {
    padding: 20rpx;
    gap: 20rpx;
  }
  
  .item-name {
    font-size: 30rpx;
  }
  
  .stock-quantity {
    font-size: 26rpx;
  }
}
