<!--pages/material/inbound/list.wxml-->
<view class="page-container">
  
  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索入库单号或供应商"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" class="search-actions">
        <text bind:tap="onSearch">搜索</text>
        <van-icon name="filter-o" size="16" bind:tap="onShowStatusFilter" />
      </view>
    </van-search>
  </view>

  <!-- 入库单列表 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y="true"
      bindscrolltolower="onLoadMore"
      refresher-enabled="true"
      refresher-triggered="{{ refreshing }}"
      bindrefresherrefresh="onRefresh"
    >
      <!-- 使用 van-cell-group 包装列表 -->
      <van-cell-group wx:if="{{ inboundList.length > 0 }}">
        <van-cell
          wx:for="{{ inboundList }}"
          wx:key="inboundId"
          custom-class="inbound-item"
          bind:click="onInboundItemClick"
          data-item="{{ item }}"
          is-link
          use-slot
        >
          <!-- 自定义内容 -->
          <view class="item-content">
            <!-- 入库单信息 -->
            <view class="item-info">
              <view class="item-header">
                <text class="item-id">{{ item.inboundId }}</text>
                <view class="item-tags">
                  <van-tag
                    type="{{ item.inboundType === 1 ? 'primary' : (item.inboundType === 2 ? 'warning' : 'default') }}"
                    size="mini"
                  >
                    {{ item.inboundTypeName }}
                  </van-tag>
                  <van-tag
                    type="{{ item.status === 1 ? 'default' : (item.status === 2 ? 'warning' : (item.status === 3 ? 'primary' : (item.status === 4 ? 'success' : 'danger'))) }}"
                    size="mini"
                  >
                    {{ item.statusName }}
                  </van-tag>
                </view>
              </view>
              
              <view class="item-details">
                <text class="item-supplier">供应商: {{ item.supplierName }}</text>
                <text class="item-date">业务日期: {{ item.businessDate }}</text>
                <text class="item-creator">制单人: {{ item.creatorName }}</text>
              </view>
              
              <view class="item-summary">
                <text class="item-count">物品数量: {{ item.itemCount }} 种</text>
                <text class="item-amount">总金额: ¥{{ item.totalAmount }}</text>
              </view>
              
              <view class="item-extra" wx:if="{{ item.remark }}">
                <text class="item-remark">备注: {{ item.remark }}</text>
              </view>
              
              <view class="item-time">
                <text class="create-time">创建时间: {{ item.createTime }}</text>
              </view>
            </view>
          </view>
        </van-cell>
      </van-cell-group>

      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{ loading }}">
        <van-loading type="spinner" size="24px">加载中...</van-loading>
      </view>

      <!-- 加载完成提示 -->
      <view class="finished-container" wx:if="{{ finished && inboundList.length > 0 }}">
        <text class="finished-text">没有更多了</text>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <van-empty
      wx:if="{{ !loading && inboundList.length === 0 }}"
      description="暂无入库单数据"
      image="search"
    />
  </view>

  <!-- 固定新增按钮 -->
  <view class="fixed-add-button">
    <van-button
      type="primary"
      round
      icon="plus"
      bind:click="onAddInbound"
      custom-class="add-button"
    >
      新增
    </van-button>
  </view>

  <!-- 状态筛选器 -->
  <van-action-sheet
    show="{{ showStatusFilter }}"
    title="选择状态"
    bind:close="onStatusFilterCancel"
    bind:cancel="onStatusFilterCancel"
  >
    <view class="status-filter-content">
      <van-cell-group>
        <van-cell
          wx:for="{{ statusOptions }}"
          wx:key="value"
          title="{{ item.text }}"
          clickable
          bind:click="onStatusFilterConfirm"
          data-detail="{{ {value: item.value} }}"
        >
          <van-icon
            wx:if="{{ statusFilter === item.value }}"
            name="success"
            color="#1989fa"
            slot="right-icon"
          />
        </van-cell>
      </van-cell-group>
    </view>
  </van-action-sheet>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
  
  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>
