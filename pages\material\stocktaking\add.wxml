<!--pages/material/stocktaking/add.wxml-->
<view class="page-container">
  <!-- 基本信息表单 -->
  <view class="form-section">
    <view class="section-title">基本信息</view>

    <van-cell-group>
      <!-- 盘点名称 -->
      <van-field
        label="盘点名称"
        value="{{ formData.stocktakingName }}"
        placeholder="请输入盘点名称"
        required
        data-field="stocktakingName"
        bind:change="onFieldChange"
      />

      <!-- 盘点类型 -->
      <van-cell
        title="盘点类型"
        value="{{ stocktakingTypeOptions[formData.stocktakingType - 1].text }}"
        is-link
        required
        bind:click="onShowTypePicker"
      />

      <!-- 仓库选择（抽盘和循环盘点时显示） -->
      <van-cell
        wx:if="{{ formData.stocktakingType !== 1 }}"
        title="仓库"
        value="{{ warehouseName || '请选择仓库' }}"
        is-link
        required
        bind:click="onShowWarehousePicker"
      />

      <!-- 计划开始时间 -->
      <van-cell
        title="计划开始时间"
        value="{{ formData.planStartTime }}"
        is-link
        required
        bind:click="onShowStartTimePicker"
      />

      <!-- 计划结束时间 -->
      <van-cell
        title="计划结束时间"
        value="{{ formData.planEndTime }}"
        is-link
        required
        bind:click="onShowEndTimePicker"
      />

      <!-- 备注 -->
      <van-field
        label="备注"
        value="{{ formData.remark }}"
        placeholder="请输入备注信息"
        type="textarea"
        autosize
        data-field="remark"
        bind:change="onFieldChange"
      />
    </van-cell-group>
  </view>

  <!-- 底部按钮 -->
  <view class="footer-buttons">
    <van-button
      type="default"
      size="large"
      bind:click="onCancel"
    >取消</van-button>
    <van-button
      type="primary"
      size="large"
      loading="{{ submitting }}"
      bind:click="onSave"
    >保存</van-button>
  </view>

  <!-- 盘点类型选择器 -->
  <van-popup
    show="{{ showTypePicker }}"
    position="bottom"
    bind:close="onTypeCancel"
  >
    <van-picker
      columns="{{ stocktakingTypeOptions }}"
      default-index="{{ formData.stocktakingType - 1 }}"
      bind:confirm="onTypeConfirm"
      bind:cancel="onTypeCancel"
      show-toolbar
      title="选择盘点类型"
    />
  </van-popup>

  <!-- 仓库选择弹窗 -->
  <van-popup
    show="{{ showWarehousePicker }}"
    position="bottom"
    bind:close="onCloseWarehousePicker"
  >
    <view class="picker-container">
      <view class="picker-header">
        <view class="picker-title">选择仓库</view>
        <van-icon name="cross" bind:click="onCloseWarehousePicker" />
      </view>

      <view class="picker-content">
        <van-cell-group>
          <van-cell
            wx:for="{{ warehouseList }}"
            wx:key="warehouseId"
            title="{{ item.warehouseName }}"
            bind:click="onWarehouseSelect"
            data-warehouse="{{ item }}"
            is-link
          />
        </van-cell-group>
      </view>
    </view>
  </van-popup>

  <!-- 开始时间选择器 -->
  <van-popup
    show="{{ showStartTimePicker }}"
    position="bottom"
    bind:close="onStartTimeCancel"
  >
    <van-datetime-picker
      type="datetime"
      value="{{ minDate }}"
      min-date="{{ minDate }}"
      max-date="{{ maxDate }}"
      bind:confirm="onStartTimeConfirm"
      bind:cancel="onStartTimeCancel"
    />
  </van-popup>

  <!-- 结束时间选择器 -->
  <van-popup
    show="{{ showEndTimePicker }}"
    position="bottom"
    bind:close="onEndTimeCancel"
  >
    <van-datetime-picker
      type="datetime"
      value="{{ minDate }}"
      min-date="{{ minDate }}"
      max-date="{{ maxDate }}"
      bind:confirm="onEndTimeConfirm"
      bind:cancel="onEndTimeCancel"
    />
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />

  <!-- Dialog 组件 -->
  <van-dialog id="van-dialog" />
</view>