// pages/material/stocktaking/record.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { getStocktakingDetails, recordStocktaking } from '../../../api/material.js'

Page({
  data: {
    // 盘点单ID
    stocktakingId: '',

    // 盘点明细列表
    detailList: [],

    // 加载状态
    loading: true,

    // 当前录入的明细
    currentDetail: null,

    // 显示录入弹窗
    showRecordDialog: false,

    // 录入数据
    recordData: {
      actualQuantity: '',
      differenceReason: ''
    }
  },

  onLoad(options) {
    console.log('📋 盘点记录页面加载', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取盘点单ID
    const { stocktakingId } = options
    if (!stocktakingId) {
      Toast.fail('盘点单ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ stocktakingId })

    // 加载盘点明细
    this.loadStocktakingDetails()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadStocktakingDetails().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 加载盘点明细
   */
  async loadStocktakingDetails() {
    this.setData({ loading: true })

    try {
      console.log('🔍 加载盘点明细:', this.data.stocktakingId)

      const response = await getStocktakingDetails(this.data.stocktakingId)

      if (response && response.code === 200 && response.data) {
        console.log('📋 获取到盘点明细:', response.data)

        this.setData({
          detailList: Array.isArray(response.data) ? response.data : []
        })
      } else {
        throw new Error(response?.msg || '获取盘点明细失败')
      }
    } catch (error) {
      console.error('❌ 加载盘点明细失败:', error)
      Toast.fail('获取明细失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 录入盘点结果
   */
  onRecordItem(event) {
    const detail = event.currentTarget.dataset.detail
    console.log('录入盘点结果:', detail)

    this.setData({
      currentDetail: detail,
      'recordData.actualQuantity': detail.actualQuantity || '',
      'recordData.differenceReason': detail.differenceReason || '',
      showRecordDialog: true
    })
  },

  /**
   * 录入数据变化
   */
  onRecordDataChange(event) {
    const { field } = event.currentTarget.dataset
    const value = event.detail

    this.setData({
      [`recordData.${field}`]: value
    })
  },

  /**
   * 确认录入
   */
  async onConfirmRecord() {
    const { currentDetail, recordData } = this.data

    // 验证实盘数量
    if (!recordData.actualQuantity || recordData.actualQuantity === '') {
      Toast.fail('请输入实盘数量')
      return
    }

    const actualQuantity = parseFloat(recordData.actualQuantity)
    if (isNaN(actualQuantity) || actualQuantity < 0) {
      Toast.fail('实盘数量必须为非负数')
      return
    }

    try {
      // 构建录入数据
      const submitData = {
        detailId: currentDetail.detailId,
        actualQuantity: actualQuantity,
        differenceReason: recordData.differenceReason.trim()
      }

      console.log('💾 提交盘点记录:', submitData)

      // 调用API录入盘点结果
      const response = await recordStocktaking(submitData)

      if (response && response.code === 200) {
        Toast.success('录入成功')

        // 关闭弹窗
        this.setData({ showRecordDialog: false })

        // 刷新明细列表
        this.loadStocktakingDetails()
      } else {
        throw new Error(response?.msg || '录入失败')
      }
    } catch (error) {
      console.error('❌ 录入盘点结果失败:', error)
      Toast.fail(error.message || '录入失败')
    }
  },

  /**
   * 取消录入
   */
  onCancelRecord() {
    this.setData({
      showRecordDialog: false,
      currentDetail: null,
      'recordData.actualQuantity': '',
      'recordData.differenceReason': ''
    })
  }
})