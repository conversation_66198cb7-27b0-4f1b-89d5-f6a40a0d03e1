// pages/material/inventory/detail.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import { getItemDetail } from '../../../api/material.js'

Page({
  data: {
    itemId: '',
    itemDetail: null,
    loading: true,

    // 页面显示控制
    showBasicInfo: true,
    showStockInfo: true,
    showWarehouseInfo: true,
    showTechnicalInfo: false
  },

  onLoad(options) {
    console.log('📋 库存详情页面加载，参数:', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取物品ID
    const { itemId } = options
    if (!itemId) {
      console.error('❌ 物品ID为空')
      Toast.fail('物品ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('✅ 物品ID:', itemId)
    this.setData({ itemId })
    this.loadItemDetail()
  },

  /**
   * 加载物品详情
   */
  async loadItemDetail() {
    try {
      this.setData({ loading: true })

      console.log('📋 开始加载物品详情:', this.data.itemId)

      const response = await getItemDetail(this.data.itemId)

      if (response && response.code === 200 && response.data) {
        const itemDetail = response.data

        // 处理数据，确保所有字段都有默认值
        const processedDetail = {
          ...itemDetail,
          itemTypeName: itemDetail.itemTypeName || (itemDetail.itemType === 1 ? '消耗品' : '备品备件'),
          specModel: itemDetail.specModel || '未指定',
          unit: itemDetail.unit || '个',
          imageUrl: itemDetail.imageUrl || '/images/default-avatar.png',
          totalQuantity: itemDetail.totalQuantity || 0,
          safetyStock: itemDetail.safetyStock || 0,
          hasExpiry: itemDetail.hasExpiry || 0,
          expiryPeriod: itemDetail.expiryPeriod || null,
          storageCondition: itemDetail.storageCondition || '常温存储',
          applicableDevice: itemDetail.applicableDevice || '通用',
          partCategoryName: itemDetail.partCategoryName || '常用',
          replacementCycle: itemDetail.replacementCycle || null,
          remark: itemDetail.remark || '无',
          inventoryList: itemDetail.inventoryList || []
        }

        this.setData({
          itemDetail: processedDetail
        })

        // 更新页面标题
        wx.setNavigationBarTitle({
          title: processedDetail.itemName || '物品详情'
        })

        console.log('✅ 物品详情加载成功:', processedDetail)

      } else {
        throw new Error(response?.msg || '获取物品详情失败')
      }

    } catch (error) {
      console.error('❌ 加载物品详情失败:', error)

      // 如果是网络错误，显示模拟数据
      if (error.message && (error.message.includes('网络') || error.message.includes('超时'))) {
        console.log('🔄 网络异常，使用模拟数据')
        this.loadMockData()
        Toast.fail('网络异常，显示离线数据')
      } else {
        Toast.fail(error.message || '加载失败')
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载模拟数据（网络异常时的备用方案）
   */
  loadMockData() {
    const mockDetail = {
      itemId: this.data.itemId,
      itemName: '螺丝刀',
      itemCode: 'SD001',
      itemType: 2,
      itemTypeName: '备品备件',
      specModel: '十字头 6mm',
      unit: '把',
      imageUrl: '/images/default-avatar.png',
      totalQuantity: 100.00,
      safetyStock: 10.00,
      hasExpiry: 0,
      expiryPeriod: null,
      storageCondition: '常温存储',
      applicableDevice: '设备A型号',
      partCategory: 1,
      partCategoryName: '关键',
      replacementCycle: 30,
      remark: '重要备件',
      createTime: '2024-01-01 10:00:00',
      createBy: 'admin',
      inventoryList: [
        {
          inventoryId: 'INV001',
          itemId: this.data.itemId,
          warehouseId: 1,
          warehouseName: '主仓库',
          currentQuantity: 50.00,
          safetyStock: 5.00,
          stockStatus: 1,
          stockStatusName: '正常',
          shelfLocation: 'A-01-01'
        },
        {
          inventoryId: 'INV002',
          itemId: this.data.itemId,
          warehouseId: 2,
          warehouseName: '备用仓库',
          currentQuantity: 50.00,
          safetyStock: 5.00,
          stockStatus: 1,
          stockStatusName: '正常',
          shelfLocation: 'B-02-03'
        }
      ]
    }

    this.setData({
      itemDetail: mockDetail
    })

    wx.setNavigationBarTitle({
      title: mockDetail.itemName
    })
  },

  /**
   * 折叠面板变化
   */
  onCollapseChange(event) {
    const value = event.detail
    this.setData({
      showTechnicalInfo: value.includes('technical')
    })
  },

  /**
   * 查看仓库详情
   */
  onWarehouseClick(event) {
    const warehouse = event.currentTarget.dataset.warehouse
    console.log('点击仓库:', warehouse)

    // 显示仓库详细信息
    wx.showModal({
      title: warehouse.warehouseName,
      content: `库存数量: ${warehouse.currentQuantity} ${this.data.itemDetail.unit}\n安全库存: ${warehouse.safetyStock} ${this.data.itemDetail.unit}\n货架位置: ${warehouse.shelfLocation}\n库存状态: ${warehouse.stockStatusName}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 复制物品编码
   */
  onCopyItemCode() {
    if (!this.data.itemDetail?.itemCode) {
      Toast.fail('物品编码为空')
      return
    }

    wx.setClipboardData({
      data: this.data.itemDetail.itemCode,
      success: () => {
        Toast.success('编码已复制')
      },
      fail: () => {
        Toast.fail('复制失败')
      }
    })
  },

  /**
   * 分享物品信息
   */
  onShareItem() {
    const item = this.data.itemDetail
    if (!item) {
      Toast.fail('物品信息不完整')
      return
    }

    const shareContent = `物品名称: ${item.itemName}\n物品编码: ${item.itemCode}\n规格型号: ${item.specModel}\n总库存: ${item.totalQuantity} ${item.unit}\n安全库存: ${item.safetyStock} ${item.unit}`

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    // 也可以复制到剪贴板
    wx.setClipboardData({
      data: shareContent,
      success: () => {
        Toast.success('物品信息已复制到剪贴板')
      }
    })
  },

  /**
   * 页面分享配置
   */
  onShareAppMessage() {
    const item = this.data.itemDetail
    if (!item) {
      return {
        title: '库存详情',
        path: `/pages/material/inventory/detail?itemId=${this.data.itemId}`
      }
    }

    return {
      title: `${item.itemName} - 库存详情`,
      path: `/pages/material/inventory/detail?itemId=${this.data.itemId}`,
      imageUrl: item.imageUrl
    }
  }
})
