/* pages/index/index.wxss */

/* ==================== 页面容器 ==================== */
.page-container {
  min-height: 100vh;
  background-color: var(--van-background-color);
}

.content-container {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

/* ==================== 用户欢迎区域 ==================== */
.welcome-section {
  padding: var(--spacing-lg) 0;
}

.welcome-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--van-text-color);
  margin-bottom: var(--spacing-xs);
}

.welcome-subtitle {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--van-border-color);
}

.avatar-img {
  width: 100%;
  height: 100%;
}



/* ==================== 功能宫格 ==================== */
.grid-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.title-text {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.title-more {
  font-size: var(--font-size-sm);
  color: var(--van-primary-color);
}

.grid-item {
  background-color: var(--van-background-2) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  transition: all var(--animation-duration-fast) !important;
}

.grid-item:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-base) !important;
}

.grid-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  position: relative;
}

.grid-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.grid-icon--primary { background-color: rgba(25, 137, 250, 0.1); color: var(--van-primary-color); }
.grid-icon--success { background-color: rgba(7, 193, 96, 0.1); color: var(--van-success-color); }
.grid-icon--warning { background-color: rgba(255, 151, 106, 0.1); color: var(--van-warning-color); }
.grid-icon--danger { background-color: rgba(238, 10, 36, 0.1); color: var(--van-danger-color); }
.grid-icon--info { background-color: rgba(25, 137, 250, 0.1); color: var(--van-info-color); }

.grid-text {
  font-size: var(--font-size-sm);
  color: var(--van-text-color);
  text-align: center;
  line-height: var(--line-height-sm);
}

.grid-badge {
  position: absolute !important;
  top: 8rpx !important;
  right: 8rpx !important;
}



/* ==================== 快速操作 ==================== */
.quick-actions {
  margin-bottom: var(--spacing-lg);
}

.action-item {
  background-color: var(--van-background-2) !important;
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  transition: all var(--animation-duration-fast) !important;
}

.action-item:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-base) !important;
}

.action-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-md);
}

.action-text {
  font-size: var(--font-size-md);
  color: var(--van-text-color);
  font-weight: 500;
}



/* ==================== 调试区域 ==================== */
.debug-section {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
}

.debug-info {
  background-color: var(--van-background-2);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  border: 1px dashed var(--van-border-color);
}

.debug-text {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
  margin-bottom: var(--spacing-xs);
}

.debug-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

/* ==================== 动画效果 ==================== */
.welcome-section {
  animation: fadeInDown 0.6s ease-out;
}

.grid-section {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.quick-actions {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.debug-section {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
