# 微信小程序相关
.DS_Store
Thumbs.db

# 依赖目录
node_modules/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/

# nyc 测试覆盖率
.nyc_output

# Grunt 中间和输出目录
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 微任务缓存目录
.microbundle-cache

# 可选的 REPL 历史
.node_repl_history

# yarn 完整性文件
.yarn-integrity

# dotenv 环境变量文件
.env

# next.js 构建输出
.next

# nuxt.js 构建输出
.nuxt

# vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 微信开发者工具相关
project.private.config.json

# 临时文件
*.tmp
*.temp

# 文档目录
doc/
