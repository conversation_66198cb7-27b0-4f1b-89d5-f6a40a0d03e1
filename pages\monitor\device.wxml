<!--pages/monitor/device.wxml-->
<view class="device-monitor-page">

  <!-- 搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{searchKeyword}}"
      placeholder="搜索设备名称"
      use-action-slot
      bind:search="onSearchDevice"
      bind:clear="onClearSearch"
      bind:change="onSearchDevice"
    >
      <view slot="action" class="action-buttons">
        <view bind:tap="onDebugApi" class="debug-btn">
          <van-icon name="info-o" size="16" />
        </view>
        <view bind:tap="onRefreshCache" class="refresh-btn">
          <van-icon name="refresh" size="18" />
        </view>
      </view>
    </van-search>
  </view>

  <!-- 设备列表 -->
  <view class="device-list-section">

    <!-- 加载状态 -->
    <view wx:if="{{loading && isFirstLoad}}" class="loading-container">
      <van-loading type="spinner" size="24" text-size="14">加载中...</van-loading>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{isEmpty}}" class="empty-container">
      <van-empty
        image="search"
        description="暂无设备数据"
      >
        <van-button
          round
          type="primary"
          size="small"
          bind:click="onRefreshCache"
        >
          刷新数据
        </van-button>
      </van-empty>
    </view>

    <!-- 设备卡片列表 -->
    <view wx:else class="device-cards">
      <view
        wx:for="{{deviceList}}"
        wx:key="id"
        class="device-card-wrapper"
        bind:tap="onDeviceClick"
        data-device="{{item}}"
      >
        <van-card
          title="{{item.deviceName}}"
          desc="{{item.location || '位置未设置'}}"
          thumb="{{item.icon || '/images/default-avatar.png'}}"
          custom-class="device-card"
        >
        <!-- 设备状态标签 -->
        <view slot="tags">
          <van-tag
            type="{{item.deviceStatus === 1 ? 'success' : 'default'}}"
            size="medium"
          >
            {{item.deviceStatus === 1 ? '在线' : '离线'}}
          </van-tag>

          <!-- 运行状态标签 -->
          <van-tag
            wx:if="{{item.runningStatus}}"
            type="{{item.runningStatus === 'normal' ? 'primary' : 'warning'}}"
            size="medium"
            custom-class="status-tag"
          >
            {{item.runningStatus === 'normal' ? '正常' : '异常'}}
          </van-tag>
        </view>

        <!-- 设备信息 -->
        <view slot="footer" class="device-info">
          <view class="info-item">
            <text class="info-label">设备ID:</text>
            <text class="info-value">{{item.id}}</text>
          </view>
        </view>
        </van-card>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view wx:if="{{loading && !isFirstLoad}}" class="load-more-container">
      <van-loading type="spinner" size="16" text-size="12">加载更多...</van-loading>
    </view>

    <!-- 没有更多数据提示 -->
    <view wx:elif="{{!pagination.hasMore && deviceList.length > 0}}" class="no-more-container">
      <van-divider>没有更多数据了</van-divider>
    </view>

  </view>

</view>