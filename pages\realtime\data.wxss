/* pages/realtime/data.wxss */

/* ==================== 页面容器 ==================== */
.realtime-data-page {
  min-height: 100vh;
  background-color: var(--van-background-color);
}

/* ==================== 页面头部 ==================== */
.header-section {
  background-color: var(--van-background-2);
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-light);
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.device-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
}

.device-id {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
}

.update-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.time-text {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-3);
}

/* ==================== 标签页内容 ==================== */
.tab-content {
  padding: var(--spacing-md);
  min-height: 60vh;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl) 0;
}

/* ==================== 错误状态 ==================== */
.error-container {
  padding: var(--spacing-xxl) 0;
}

/* ==================== 参数列表 ==================== */
.params-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.params-group {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--shadow-light) !important;
  overflow: hidden !important;
}

.param-cell {
  transition: background-color var(--animation-duration-fast) !important;
}

.param-cell:active {
  background-color: var(--van-active-color) !important;
}

/* ==================== 参数值样式 ==================== */
.param-value-container {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.param-value {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--van-text-color);
  font-family: 'Courier New', monospace;
}

.param-value--warning {
  color: var(--van-warning-color);
}

.param-value--danger {
  color: var(--van-danger-color);
}

.param-unit {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
  font-weight: normal;
}

.alert-tag {
  margin-left: var(--spacing-xs) !important;
}

/* ==================== 告警参数样式 ==================== */
.alert-param-cell {
  position: relative !important;
}

.param-range {
  font-size: var(--font-size-xs);
  color: var(--van-text-color-3);
  margin-top: var(--spacing-xs);
}

/* ==================== 告警状态容器 ==================== */
.alert-status-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

/* ==================== 告警状态显示 ==================== */
.alert-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.status-indicator {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* 正常状态 */
.alert-status--normal {
  background-color: rgba(7, 193, 96, 0.1);
}

.alert-status--normal .status-indicator {
  background-color: var(--van-success-color);
}

.alert-status--normal .status-text {
  color: var(--van-success-color);
}

/* 告警状态 */
.alert-status--danger {
  background-color: rgba(238, 10, 36, 0.1);
}

.alert-status--danger .status-indicator {
  background-color: var(--van-danger-color);
}

.alert-status--danger .status-text {
  color: var(--van-danger-color);
}

/* ==================== 参数值信息 ==================== */
.param-value-info {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

/* ==================== 控制参数样式 ==================== */
.control-param-cell {
  position: relative !important;
}

.control-param-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
  min-width: 200rpx;
}

/* ==================== 参数显示模式 ==================== */
.param-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.edit-btn {
  margin: 0 !important;
}

.readonly-tag {
  margin: 0 !important;
}

/* ==================== 参数编辑模式 ==================== */
.param-edit {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.edit-input-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.edit-input {
  flex: 1 !important;
  min-width: 120rpx !important;
}

.input-unit {
  font-size: var(--font-size-sm);
  color: var(--van-text-color-2);
  min-width: 40rpx;
}

.edit-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

.cancel-btn,
.save-btn {
  margin: 0 !important;
  min-width: 80rpx !important;
}

/* ==================== 空状态 ==================== */
.empty-params {
  padding: var(--spacing-xxl) 0;
}

.coming-soon {
  padding: var(--spacing-xxl) 0;
}

/* ==================== 实时数据动画 ==================== */
.param-value {
  animation: dataUpdate 0.3s ease-in-out;
}

@keyframes dataUpdate {
  0% {
    background-color: rgba(25, 137, 250, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* ==================== 参数状态指示器 ==================== */
.param-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.param-status::before {
  content: '';
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: var(--van-success-color);
}

.param-status--warning::before {
  background-color: var(--van-warning-color);
}

.param-status--error::before {
  background-color: var(--van-danger-color);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .tab-content {
    padding: var(--spacing-sm);
  }

  .param-value-container {
    flex-direction: column;
    align-items: flex-end;
    gap: 2rpx;
  }

  .control-param-container {
    min-width: 160rpx;
  }

  .param-display {
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-xs);
  }

  .edit-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xs);
  }

  .edit-input {
    min-width: auto !important;
  }
}

/* ==================== 数据刷新提示 ==================== */
.refresh-indicator {
  position: fixed;
  top: 100rpx;
  right: var(--spacing-md);
  background-color: var(--van-primary-color);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  z-index: 1000;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0;
    transform: translateX(100%);
  }
  10%, 90% {
    opacity: 1;
    transform: translateX(0);
  }
}
