<!--pages/material/inventory/detail.wxml-->
<view class="page-container">

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </view>

  <!-- 详情内容 -->
  <scroll-view class="detail-content" scroll-y="true" wx:if="{{ !loading && itemDetail }}">

    <!-- 物品基本信息 -->
    <view class="section-container">
      <view class="section-header">
        <view class="header-content">
          <van-image
            src="{{ itemDetail.imageUrl }}"
            width="120rpx"
            height="120rpx"
            radius="12rpx"
            error-icon="photo-fail"
            custom-class="item-image"
          />
          <view class="header-info">
            <text class="item-name">{{ itemDetail.itemName }}</text>
            <view class="item-code-row">
              <text class="item-code">编码: {{ itemDetail.itemCode }}</text>
              <van-icon name="copy" size="16" color="#1989fa" bind:tap="onCopyItemCode" />
            </view>
            <view class="item-tags">
              <van-tag type="primary" size="mini">{{ itemDetail.itemTypeName }}</van-tag>
              <van-tag
                type="{{ itemDetail.totalQuantity > itemDetail.safetyStock ? 'success' : 'danger' }}"
                size="mini"
              >
                {{ itemDetail.totalQuantity > itemDetail.safetyStock ? '库存正常' : '库存不足' }}
              </van-tag>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 库存概览 -->
    <view class="section-container">
      <view class="section-title">
        <van-icon name="records" size="16" />
        <text>库存概览</text>
      </view>

      <van-cell-group>
        <van-cell title="总库存" value="{{ itemDetail.totalQuantity }} {{ itemDetail.unit }}" />
        <van-cell title="安全库存" value="{{ itemDetail.safetyStock }} {{ itemDetail.unit }}" />
        <van-cell title="分布仓库" value="{{ itemDetail.inventoryList.length }} 个" />
        <van-cell title="规格型号" value="{{ itemDetail.specModel }}" />
      </van-cell-group>
    </view>

    <!-- 仓库分布 -->
    <view class="section-container">
      <view class="section-title">
        <van-icon name="shop-o" size="16" />
        <text>仓库分布</text>
      </view>

      <van-cell-group>
        <van-cell
          wx:for="{{ itemDetail.inventoryList }}"
          wx:key="inventoryId"
          title="{{ item.warehouseName }}"
          label="货架: {{ item.shelfLocation }}"
          value="{{ item.currentQuantity }} {{ itemDetail.unit }}"
          is-link
          bind:click="onWarehouseClick"
          data-warehouse="{{ item }}"
        >
          <view slot="right-icon">
            <van-tag
              type="{{ item.stockStatus === 1 ? 'success' : 'danger' }}"
              size="mini"
            >
              {{ item.stockStatusName }}
            </van-tag>
          </view>
        </van-cell>
      </van-cell-group>
    </view>

    <!-- 技术信息 -->
    <view class="section-container">
      <van-collapse value="{{ showTechnicalInfo ? ['technical'] : [] }}" bind:change="onCollapseChange">
        <van-collapse-item title="技术信息" name="technical" icon="info-o">
          <van-cell-group>
            <van-cell title="适用设备" value="{{ itemDetail.applicableDevice }}" />
            <van-cell title="备件分类" value="{{ itemDetail.partCategoryName }}" />
            <van-cell
              title="更换周期"
              value="{{ itemDetail.replacementCycle ? itemDetail.replacementCycle + ' 天' : '无' }}"
            />
            <van-cell title="存储条件" value="{{ itemDetail.storageCondition }}" />
            <van-cell
              title="有效期"
              value="{{ itemDetail.hasExpiry ? (itemDetail.expiryPeriod + ' 天') : '无限期' }}"
            />
            <van-cell title="备注" value="{{ itemDetail.remark }}" />
          </van-cell-group>
        </van-collapse-item>
      </van-collapse>
    </view>

    <!-- 创建信息 -->
    <view class="section-container">
      <view class="section-title">
        <van-icon name="clock-o" size="16" />
        <text>创建信息</text>
      </view>

      <van-cell-group>
        <van-cell title="创建时间" value="{{ itemDetail.createTime }}" />
        <van-cell title="创建人" value="{{ itemDetail.createBy }}" />
      </van-cell-group>
    </view>

    <!-- 底部间距 -->
    <view class="bottom-spacing"></view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{ !loading && !itemDetail }}">
    <van-icon name="warning-o" size="48" color="#ddd" />
    <text class="empty-text">未找到物品信息</text>
  </view>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
