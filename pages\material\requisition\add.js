// pages/material/requisition/add.js
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
import { checkPagePermission } from '../../../utils/permission.js'
import { addRequisition, getItemSelectList } from '../../../api/material.js'

Page({
  data: {
    // 表单数据
    formData: {
      businessDate: '',
      applicantId: null,
      deptId: null,
      requisitionPurpose: ''
    },
    
    // 领用明细列表
    detailList: [],
    
    // 物品选择相关
    showItemPicker: false,
    itemList: [],
    itemSearchKeyword: '',
    itemLoading: false,
    
    // 仓库选择相关
    showWarehousePicker: false,
    warehouseList: [
      { warehouseId: 1, warehouseName: '主仓库' },
      { warehouseId: 2, warehouseName: '备用仓库' }
    ],
    
    // 申请人选择相关
    showApplicantPicker: false,
    applicantList: [
      { userId: 1, userName: '张三', deptName: '技术部' },
      { userId: 2, userName: '李四', deptName: '运营部' },
      { userId: 3, userName: '王五', deptName: '财务部' }
    ],
    
    // 部门选择相关
    showDeptPicker: false,
    deptList: [
      { deptId: 1, deptName: '技术部' },
      { deptId: 2, deptName: '运营部' },
      { deptId: 3, deptName: '财务部' },
      { deptId: 4, deptName: '行政部' }
    ],
    
    // 日期选择
    showDatePicker: false,
    minDate: new Date(2020, 0, 1).getTime(),
    maxDate: new Date(2030, 11, 31).getTime(),
    
    // 页面状态
    submitting: false
  },

  onLoad() {
    console.log('📋 新增领用单页面加载')
    
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 设置默认业务日期为今天
    const today = new Date()
    const dateStr = today.getFullYear() + '-' + 
                   String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(today.getDate()).padStart(2, '0')
    
    this.setData({
      'formData.businessDate': dateStr
    })
    
    console.log('✅ 页面数据初始化完成')
  },

  /**
   * 表单字段变化处理
   */
  onFieldChange(event) {
    const { field } = event.currentTarget.dataset
    const value = event.detail
    
    this.setData({
      [`formData.${field}`]: value
    })
    
    console.log(`📝 字段 ${field} 更新为:`, value)
  },

  /**
   * 显示日期选择器
   */
  onShowDatePicker() {
    this.setData({ showDatePicker: true })
  },

  /**
   * 日期选择确认
   */
  onDateConfirm(event) {
    const date = new Date(event.detail)
    const dateStr = date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0')
    
    this.setData({
      'formData.businessDate': dateStr,
      showDatePicker: false
    })
  },

  /**
   * 日期选择取消
   */
  onDateCancel() {
    this.setData({ showDatePicker: false })
  },

  /**
   * 显示申请人选择器
   */
  onShowApplicantPicker() {
    this.setData({ showApplicantPicker: true })
  },

  /**
   * 申请人选择
   */
  onApplicantSelect(event) {
    const applicant = event.currentTarget.dataset.applicant
    console.log('选择申请人:', applicant)
    
    this.setData({
      'formData.applicantId': applicant.userId,
      applicantName: applicant.userName,
      showApplicantPicker: false
    })
  },

  /**
   * 关闭申请人选择器
   */
  onCloseApplicantPicker() {
    this.setData({ showApplicantPicker: false })
  },

  /**
   * 显示部门选择器
   */
  onShowDeptPicker() {
    this.setData({ showDeptPicker: true })
  },

  /**
   * 部门选择
   */
  onDeptSelect(event) {
    const dept = event.currentTarget.dataset.dept
    console.log('选择部门:', dept)
    
    this.setData({
      'formData.deptId': dept.deptId,
      deptName: dept.deptName,
      showDeptPicker: false
    })
  },

  /**
   * 关闭部门选择器
   */
  onCloseDeptPicker() {
    this.setData({ showDeptPicker: false })
  },

  /**
   * 添加领用明细
   */
  onAddDetail() {
    this.loadItemList()
    this.setData({ showItemPicker: true })
  },

  /**
   * 加载物品列表
   */
  async loadItemList() {
    if (this.data.itemLoading) return
    
    this.setData({ itemLoading: true })
    
    try {
      // 构建查询参数
      const params = {
        pageNum: 1,
        pageSize: 50
      }
      
      // 添加搜索关键词
      if (this.data.itemSearchKeyword) {
        params.itemName = this.data.itemSearchKeyword
        params.itemCode = this.data.itemSearchKeyword
      }
      
      console.log('🔍 查询物品列表参数:', params)
      
      // 调用API获取物品列表
      const response = await getItemSelectList(params)
      
      if (response && response.code === 200 && response.data) {
        const itemList = response.data.records || []
        console.log('📦 获取到物品列表:', itemList)
        
        this.setData({ itemList })
      } else {
        throw new Error(response?.msg || '获取物品列表失败')
      }
    } catch (error) {
      console.error('❌ 加载物品列表失败:', error)
      Toast.fail('获取物品列表失败')
    } finally {
      this.setData({ itemLoading: false })
    }
  },

  /**
   * 物品搜索输入
   */
  onItemSearchInput(event) {
    this.setData({
      itemSearchKeyword: event.detail
    })
  },

  /**
   * 物品搜索
   */
  onItemSearch() {
    this.loadItemList()
  },

  /**
   * 物品选择
   */
  onItemSelect(event) {
    const item = event.currentTarget.dataset.item
    console.log('选择物品:', item)
    
    // 关闭物品选择器
    this.setData({ 
      showItemPicker: false,
      showWarehousePicker: true,
      currentSelectedItem: item
    })
  },

  /**
   * 仓库选择
   */
  onWarehouseSelect(event) {
    const warehouse = event.currentTarget.dataset.warehouse
    console.log('选择仓库:', warehouse)
    
    const item = this.data.currentSelectedItem
    
    // 创建新的明细项
    const newDetail = {
      itemId: item.itemId,
      itemName: item.itemName,
      itemCode: item.itemCode,
      specModel: item.specModel,
      unit: item.unit,
      warehouseId: warehouse.warehouseId,
      warehouseName: warehouse.warehouseName,
      requisitionQuantity: 1, // 默认申请数量为1
      shelfLocation: '',
      remark: ''
    }
    
    // 添加到明细列表
    const detailList = [...this.data.detailList, newDetail]
    
    this.setData({
      detailList,
      showWarehousePicker: false
    })
    
    Toast.success('已添加物品')
  },

  /**
   * 关闭物品选择器
   */
  onCloseItemPicker() {
    this.setData({ showItemPicker: false })
  },

  /**
   * 关闭仓库选择器
   */
  onCloseWarehousePicker() {
    this.setData({ showWarehousePicker: false })
  },

  /**
   * 更新明细项
   */
  onDetailFieldChange(event) {
    const { field, index } = event.currentTarget.dataset
    const value = event.detail
    
    // 更新指定字段
    this.setData({
      [`detailList[${index}].${field}`]: value
    })
  },

  /**
   * 删除明细项
   */
  onDeleteDetail(event) {
    const { index } = event.currentTarget.dataset

    Dialog.confirm({
      title: '删除确认',
      message: '确定要删除该物品吗？',
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    }).then(() => {
      // 从明细列表中移除
      const detailList = [...this.data.detailList]
      detailList.splice(index, 1)

      this.setData({ detailList })
      Toast.success('已删除')
    }).catch(() => {
      // 取消删除
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { formData, detailList } = this.data

    // 验证基本信息
    if (!formData.businessDate) {
      Toast.fail('请选择业务日期')
      return false
    }

    if (!formData.applicantId) {
      Toast.fail('请选择申请人')
      return false
    }

    if (!formData.deptId) {
      Toast.fail('请选择申请部门')
      return false
    }

    if (!formData.requisitionPurpose || !formData.requisitionPurpose.trim()) {
      Toast.fail('请输入领用用途')
      return false
    }

    // 验证明细列表
    if (!detailList || detailList.length === 0) {
      Toast.fail('请至少添加一个领用物品')
      return false
    }

    // 验证每个明细项
    for (let i = 0; i < detailList.length; i++) {
      const detail = detailList[i]

      if (!detail.requisitionQuantity || parseFloat(detail.requisitionQuantity) <= 0) {
        Toast.fail(`第${i + 1}个物品的申请数量必须大于0`)
        return false
      }

      if (!detail.warehouseId) {
        Toast.fail(`第${i + 1}个物品未选择仓库`)
        return false
      }
    }

    return true
  },

  /**
   * 保存领用单
   */
  async onSave() {
    if (!this.validateForm()) {
      return
    }

    if (this.data.submitting) {
      return
    }

    this.setData({ submitting: true })

    try {
      // 构建领用单数据
      const requisitionData = {
        main: {
          businessDate: this.data.formData.businessDate,
          applicantId: this.data.formData.applicantId,
          deptId: this.data.formData.deptId,
          requisitionPurpose: this.data.formData.requisitionPurpose.trim()
        },
        details: this.data.detailList.map(detail => ({
          itemId: detail.itemId,
          warehouseId: detail.warehouseId,
          shelfLocation: detail.shelfLocation,
          requisitionQuantity: parseFloat(detail.requisitionQuantity),
          remark: detail.remark
        }))
      }

      console.log('💾 保存领用单数据:', requisitionData)

      // 调用API保存领用单
      const response = await addRequisition(requisitionData)

      if (response && response.code === 200) {
        Toast.success('领用单保存成功')

        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        throw new Error(response?.msg || '保存失败')
      }
    } catch (error) {
      console.error('❌ 保存领用单失败:', error)
      Toast.fail(error.message || '保存失败')
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 取消操作
   */
  onCancel() {
    if (this.data.detailList.length > 0 ||
        this.data.formData.requisitionPurpose ||
        this.data.formData.applicantId ||
        this.data.formData.deptId) {
      Dialog.confirm({
        title: '取消确认',
        message: '当前有未保存的数据，确定要取消吗？',
        confirmButtonText: '确定取消',
        cancelButtonText: '继续编辑'
      }).then(() => {
        wx.navigateBack()
      }).catch(() => {
        // 继续编辑
      })
    } else {
      wx.navigateBack()
    }
  }
})
