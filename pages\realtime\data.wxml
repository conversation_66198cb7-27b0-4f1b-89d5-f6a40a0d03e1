<!--pages/realtime/data.wxml-->
<view class="realtime-data-page">

  <!-- 页面头部信息 -->
  <view class="header-section">
    <view class="device-info">
      <text class="device-name">{{deviceName}}</text>
      <text class="device-id">ID: {{deviceId}}</text>
    </view>
    <view wx:if="{{lastUpdateTime}}" class="update-time">
      <van-icon name="clock-o" size="12" />
      <text class="time-text">{{lastUpdateTime}}</text>
    </view>
  </view>

  <!-- 实时数据刷新提示 -->
  <van-notice-bar
    wx:if="{{activeTab !== 2}}"
    text="数据每秒自动刷新，确保信息实时性"
    mode="closeable"
    background="#e1f3ff"
    color="#1989fa"
    left-icon="info-o"
  />

  <!-- 控制参数提示 -->
  <van-notice-bar
    wx:if="{{activeTab === 2}}"
    text="控制参数在执行写操作后自动刷新"
    mode="closeable"
    background="#fff7e6"
    color="#fa8c16"
    left-icon="setting-o"
  />

  <!-- 标签页 -->
  <van-tabs 
    active="{{activeTab}}" 
    bind:change="onTabChange"
    sticky
    offset-top="0"
    color="#1989fa"
    title-active-color="#1989fa"
    title-inactive-color="#969799"
  >
    
    <!-- 一般参数标签页 -->
    <van-tab title="一般参数" name="normal">
      <view class="tab-content">

        <!-- 错误状态 -->
        <view wx:if="{{normalParamsError}}" class="error-container">
          <van-empty
            image="error"
            description="加载失败"
          >
            <van-button
              round
              type="primary"
              size="small"
              bind:click="onRetryLoad"
            >
              重新加载
            </van-button>
          </van-empty>
        </view>

        <!-- 参数列表 -->
        <view wx:else class="params-list">
          <van-cell-group wx:if="{{normalParams.length > 0}}" custom-class="params-group">
            <van-cell
              wx:for="{{normalParams}}"
              wx:key="id"
              title="{{item.name}}"
              custom-class="param-cell"
            >
              <view slot="right-icon" class="param-value-container">
                <text class="param-value {{item.type === 'warning' ? 'param-value--warning' : ''}}">
                  {{item.value}}
                </text>
                <text wx:if="{{item.unit}}" class="param-unit">{{item.unit}}</text>
                <van-tag
                  wx:if="{{item.alertValue}}"
                  type="warning"
                  size="mini"
                  custom-class="alert-tag"
                >
                  告警
                </van-tag>
              </view>
            </van-cell>
          </van-cell-group>

          <!-- 空状态 -->
          <view wx:else class="empty-params">
            <van-empty
              image="search"
              description="暂无参数数据"
            />
          </view>
        </view>

      </view>
    </van-tab>

    <!-- 告警参数标签页 -->
    <van-tab title="告警参数" name="alert">
      <view class="tab-content">

        <!-- 错误状态 -->
        <view wx:if="{{alertParamsError}}" class="error-container">
          <van-empty
            image="error"
            description="加载失败"
          >
            <van-button
              round
              type="primary"
              size="small"
              bind:click="onRetryLoad"
            >
              重新加载
            </van-button>
          </van-empty>
        </view>

        <!-- 告警参数列表 -->
        <view wx:else class="params-list">
          <van-cell-group wx:if="{{alertParams.length > 0}}" custom-class="params-group">
            <van-cell
              wx:for="{{alertParams}}"
              wx:key="id"
              title="{{item.name}}"
              custom-class="param-cell alert-param-cell"
            >
              <view slot="right-icon" class="alert-status-container">
                <!-- 状态显示 -->
                <view class="alert-status {{item.type === 'danger' ? 'alert-status--danger' : 'alert-status--normal'}}">
                  <view class="status-indicator"></view>
                  <text class="status-text">{{item.statusText}}</text>
                </view>

                <!-- 数值显示（如果有具体数值） -->
                <view wx:if="{{item.value !== ''}}" class="param-value-info">
                  <text class="param-value {{item.type === 'danger' ? 'param-value--danger' : item.type === 'warning' ? 'param-value--warning' : ''}}">
                    {{item.value}}
                  </text>
                  <text wx:if="{{item.unit}}" class="param-unit">{{item.unit}}</text>
                </view>
              </view>

              <!-- 告警范围显示 -->
              <view wx:if="{{item.rangeStart !== null && item.rangeEnd !== null}}" slot="label" class="param-range">
                正常范围: {{item.rangeStart}} - {{item.rangeEnd}} {{item.unit}}
              </view>
            </van-cell>
          </van-cell-group>

          <!-- 空状态 -->
          <view wx:else class="empty-params">
            <van-empty
              image="search"
              description="暂无告警参数数据"
            />
          </view>
        </view>

      </view>
    </van-tab>

    <!-- 控制参数标签页 -->
    <van-tab title="控制参数" name="control">
      <view class="tab-content">

        <!-- 错误状态 -->
        <view wx:if="{{controlParamsError}}" class="error-container">
          <van-empty
            image="error"
            description="加载失败"
          >
            <van-button
              round
              type="primary"
              size="small"
              bind:click="onRetryLoad"
            >
              重新加载
            </van-button>
          </van-empty>
        </view>

        <!-- 控制参数列表 -->
        <view wx:else class="params-list">
          <van-cell-group wx:if="{{controlParams.length > 0}}" custom-class="params-group">
            <van-cell
              wx:for="{{controlParams}}"
              wx:key="id"
              title="{{item.name}}"
              custom-class="param-cell control-param-cell"
            >
              <view slot="right-icon" class="control-param-container">

                <!-- 只读模式 -->
                <view wx:if="{{!item.isEditing}}" class="param-display">
                  <view class="param-value-info">
                    <text class="param-value {{item.type === 'warning' ? 'param-value--warning' : ''}}">
                      {{item.value}}
                    </text>
                    <text wx:if="{{item.unit}}" class="param-unit">{{item.unit}}</text>
                  </view>

                  <!-- 编辑按钮 -->
                  <van-button
                    wx:if="{{item.canWrite}}"
                    type="primary"
                    size="mini"
                    bind:click="onEditControlParam"
                    data-param-id="{{item.id}}"
                    custom-class="edit-btn"
                  >
                    设置
                  </van-button>

                  <!-- 只读标识 -->
                  <van-tag
                    wx:else
                    type="default"
                    size="mini"
                    custom-class="readonly-tag"
                  >
                    只读
                  </van-tag>
                </view>

                <!-- 编辑模式 -->
                <view wx:else class="param-edit">
                  <view class="edit-input-container">
                    <van-field
                      value="{{item.editValue}}"
                      type="number"
                      placeholder="请输入参数值"
                      bind:change="onEditValueChange"
                      data-param-id="{{item.id}}"
                      custom-class="edit-input"
                      clearable
                      focus="{{item.isEditing}}"
                    />
                    <text wx:if="{{item.unit}}" class="input-unit">{{item.unit}}</text>
                  </view>

                  <view class="edit-actions">
                    <van-button
                      type="default"
                      size="mini"
                      bind:click="onCancelEditControlParam"
                      data-param-id="{{item.id}}"
                      custom-class="cancel-btn"
                    >
                      取消
                    </van-button>
                    <van-button
                      type="primary"
                      size="mini"
                      bind:click="onSaveControlParam"
                      data-param-id="{{item.id}}"
                      custom-class="save-btn"
                    >
                      保存
                    </van-button>
                  </view>
                </view>

              </view>

              <!-- 参数范围显示 -->
              <view wx:if="{{item.rangeStart !== null && item.rangeEnd !== null}}" slot="label" class="param-range">
                设置范围: {{item.rangeStart}} - {{item.rangeEnd}} {{item.unit}}
              </view>
            </van-cell>
          </van-cell-group>

          <!-- 空状态 -->
          <view wx:else class="empty-params">
            <van-empty
              image="search"
              description="暂无控制参数数据"
            />
          </view>
        </view>

      </view>
    </van-tab>

  </van-tabs>

</view>
